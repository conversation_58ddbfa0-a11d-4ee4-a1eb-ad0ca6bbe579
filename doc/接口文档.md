# Chat API 接口文档

## 基本信息

### API基础信息
- **基础路径**: `/v1`
- **API版本**: v1
- **服务描述**: 提供OpenAI兼容的聊天接口，支持流式和非流式响应
- **框架**: Spring WebFlux (响应式编程)

### 认证方式
- **认证类型**: Bearer Token
- **Header格式**: `Authorization: Bearer <your-api-key>`
- **权限要求**: 需要有效的API Key进行身份验证

### 支持的请求格式
- **Content-Type**: `application/json`
- **Accept**: 
  - `application/json` (非流式响应)
  - `text/event-stream` (流式响应)
- **字符编码**: UTF-8

## 接口详情

### 1. 创建聊天完成 (JSON格式流式响应)

#### 基本信息
- **HTTP方法**: POST
- **完整URL**: `/v1/chat/completions`
- **描述**: 创建聊天完成，流式返回以解析好的JSON格式返回，兼容OpenAI官方API格式

#### 请求参数

##### Headers
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| Authorization | string | 是 | Bearer Token认证 | `Bearer sk-xxx` |
| Content-Type | string | 是 | 请求内容类型 | `application/json` |

##### Request Body
| 参数名 | 类型 | 必填 | 默认值 | 描述 | 取值范围 |
|--------|------|------|--------|------|----------|
| messages | array | 是 | - | 消息列表 | - |
| model | string | 是 | - | 模型名称 | 不能为空 |
| stream | boolean | 否 | false | 是否流式返回 | true/false |
| max_tokens | integer | 否 | - | 最大token数 | > 0 |
| temperature | number | 否 | - | 温度参数，控制随机性 | 0.0-2.0 |
| top_p | number | 否 | - | top_p参数 | 0.0-1.0 |
| n | integer | 否 | 1 | 生成的选择数量 | >= 1 |
| stop | array | 否 | - | 停止序列 | - |
| presence_penalty | number | 否 | - | 存在惩罚 | -2.0-2.0 |
| frequency_penalty | number | 否 | - | 频率惩罚 | -2.0-2.0 |
| logit_bias | object | 否 | - | logit偏差 | - |
| user | string | 否 | - | 用户标识 | - |
| tools | array | 否 | - | 工具列表 | - |
| tool_choice | object | 否 | - | 工具选择 | - |
| response_format | object | 否 | - | 响应格式 | - |
| seed | integer | 否 | - | 种子 | - |
| stream_options | object | 否 | - | 流式选项 | - |

##### Messages 结构
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| role | string | 是 | 角色：system, user, assistant, tool |
| content | string | 否 | 消息内容 |
| tool_calls | array | 否 | 工具调用 |
| tool_call_id | string | 否 | 工具调用ID |
| name | string | 否 | 名称（function角色使用） |

#### 请求示例

```bash
curl -X POST "https://api.example.com/v1/chat/completions" \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "stream": false,
    "max_tokens": 150,
    "temperature": 0.7
  }'
```

#### 响应格式

##### 非流式响应 (stream=false)
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "gpt-3.5-turbo",
  "system_fingerprint": "fp_44709d6fcb",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

##### 流式响应 (stream=true)
每个数据块为JSON格式：
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion.chunk",
  "created": 1677652288,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "delta": {
        "content": "Hello"
      },
      "finish_reason": null
    }
  ]
}
```

### 2. 创建聊天完成 (SSE格式流式响应)

#### 基本信息
- **HTTP方法**: POST
- **完整URL**: `/v1/plain/chat/completions`
- **描述**: 创建聊天完成，流式返回以SSE格式返回，兼容OpenAI官方API格式

#### 请求参数
与第一个接口完全相同

#### 响应格式

##### 流式响应 (stream=true)
采用Server-Sent Events (SSE) 格式：
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk",...}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk",...}

data: [DONE]
```

## HTTP状态码说明

| 状态码 | 描述 | 场景 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数验证失败、请求格式错误 |
| 401 | 未授权 | API Key无效或缺失 |
| 429 | 请求过于频繁 | 触发频率限制 |
| 500 | 服务器内部错误 | 系统异常 |

## 错误响应格式

```json
{
  "code": "20000",
  "msg": "参数错误",
  "data": null
}
```

## 重要注意事项

### 流式和非流式调用的区别
- **非流式调用** (`stream=false`): 等待完整响应后一次性返回
- **流式调用** (`stream=true`): 实时返回生成的内容片段
- **使用场景**: 
  - 非流式适合短文本生成或批量处理
  - 流式适合长文本生成或需要实时显示的场景

### 请求频率限制
- **防重放机制**: 相同方法、appKey、时间戳、签名的请求只允许执行一次
- **时间窗口**: 请求时间戳必须在当前时间前后3分钟内
- **缓存时间**: 重复请求检查缓存6分钟

### 错误处理机制
- **参数验证**: 自动验证必填参数和参数格式
- **认证失败**: 返回401状态码和错误信息
- **系统异常**: 返回500状态码和通用错误信息
- **重试策略**: 建议对5xx错误进行指数退避重试

### 数据格式要求
- **字符编码**: 统一使用UTF-8编码
- **JSON格式**: 严格遵循JSON规范
- **参数命名**: 使用snake_case命名规范（如max_tokens）

### 安全相关注意事项
- **API Key保护**: 不要在客户端代码中暴露API Key
- **HTTPS传输**: 生产环境必须使用HTTPS
- **请求验证**: 所有请求都会进行身份验证和参数验证

### 兼容性说明
- **OpenAI兼容**: 完全兼容OpenAI Chat Completions API格式
- **参数支持**: 支持OpenAI API的所有标准参数
- **响应格式**: 响应格式与OpenAI API保持一致
- **工具调用**: 支持Function Calling功能

## 使用示例

### 简单对话示例
```bash
curl -X POST "https://api.example.com/v1/chat/completions" \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "什么是人工智能？"}
    ]
  }'
```

### 流式对话示例
```bash
curl -X POST "https://api.example.com/v1/chat/completions" \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "请写一首关于春天的诗"}
    ],
    "stream": true,
    "max_tokens": 200
  }'
```

### 多轮对话示例
```bash
curl -X POST "https://api.example.com/v1/chat/completions" \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "system", "content": "你是一个有用的助手"},
      {"role": "user", "content": "你好"},
      {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"},
      {"role": "user", "content": "请介绍一下机器学习"}
    ],
    "temperature": 0.7,
    "max_tokens": 500
  }'
```

---

**注意**: 本文档基于当前代码实现生成，实际使用时请以最新的API规范为准。
