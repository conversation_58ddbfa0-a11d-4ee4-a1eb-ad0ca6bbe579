package com.jd.qf.ai.gateway.facade.controller.chat;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.gateway.core.api.auth.AuthService;
import com.jd.qf.ai.gateway.core.api.auth.anno.ValidateApiKey;
import com.jd.qf.ai.gateway.core.api.auth.dto.AuthReq;
import com.jd.qf.ai.gateway.core.api.chat.OpenAIChatService;
import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionRequest;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 聊天控制器
 * 提供OpenAI兼容的聊天接口
 * <AUTHOR>
 * @description
 * @date 2025/4/2
 */
@RestController
@RequestMapping("/v1")
@Slf4j
public class ChatController {

    @Autowired
    private OpenAIChatService openAIChatService;
    @Autowired
    private AuthService authService;

    /**
     * 创建聊天完成,其中流式返回以解析好的JSON格式返回
     * 兼容OpenAI官方API格式
     * 根据stream参数决定返回流式还是非流式响应
     *
     * @param request 聊天完成请求
     * @return 聊天完成响应
     */
    @PostMapping(value = "/chat/completions", produces = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_EVENT_STREAM_VALUE
    })
    @ValidateApiKey
    public Publisher<?> createChatCompletion( @RequestBody ChatCompletionRequest request) {
        if (Boolean.TRUE.equals(request.getStream())) {
            // 流式响应
            return openAIChatService.createStreamingChatCompletion(request)
                    .map(JSON::toJSONString)
                    .subscribeOn(Schedulers.boundedElastic())
                    .doOnError(error -> log.error("流式聊天失败", error));
        } else {
            // 非流式响应
            return openAIChatService.createNonStreamingChatCompletion(request)
                    .doOnError(error -> log.error("非流式聊天失败", error));
        }
    }

    /**
     * 创建聊天完成,其中流式返回以SSE格式返回
     * 兼容OpenAI官方API格式
     * 根据stream参数决定返回流式还是非流式响应
     *
     * @param request 聊天完成请求
     * @return 聊天完成响应
     */
    @PostMapping(value = "plain/chat/completions", produces = {
            MediaType.APPLICATION_JSON_VALUE,
            MediaType.TEXT_EVENT_STREAM_VALUE
    })
    @ValidateApiKey
    public Publisher<?> createPlainChatCompletion( @RequestBody ChatCompletionRequest request) {

        if (Boolean.TRUE.equals(request.getStream())) {
            // 流式响应
            return openAIChatService.createStreamingChatCompletion(request)
                    .map(chunk -> {
                        // 转换为SSE格式
                        String json = JSON.toJSONString(chunk);
                        return "data: " + json + "\n\n";
                    })
                    .concatWith(Mono.just("data: [DONE]\n\n"))
                    .doOnError(error -> log.error("流式聊天失败", error));
        } else {
            // 非流式响应
            return openAIChatService.createNonStreamingChatCompletion(request)
                    .doOnError(error -> log.error("非流式聊天失败", error));
        }
    }
}
