<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.wdy</groupId>
        <artifactId>qf-ai-gateway-domain</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>gateway-facade</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>gateway-open-sdk</artifactId>
        <version>${open.sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>gateway-core-api</artifactId>
        </dependency>
        <!-- jsf -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wangyin.schedule</groupId>
            <artifactId>schedule-client-starter</artifactId>
        </dependency>
    </dependencies>

</project>