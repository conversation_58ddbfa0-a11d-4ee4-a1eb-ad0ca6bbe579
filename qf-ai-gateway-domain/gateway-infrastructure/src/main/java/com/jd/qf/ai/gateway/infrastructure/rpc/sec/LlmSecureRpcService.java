package com.jd.qf.ai.gateway.infrastructure.rpc.sec;

import com.jd.qf.ai.gateway.infrastructure.rpc.sec.dto.LlmSecureRequest;
import com.jd.security.llmsec.core.ResponseMessage;
import com.jd.security.llmsec.core.api.defense.DefenseApiResponseX;
import com.jd.security.llmsec.core.check.RiskCheckResult;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 大模型安全RPC服务
 * <AUTHOR>
 * @description
 * @date 2025/3/31
 */
public interface LlmSecureRpcService {

    /**
     * 执行风险检查并返回检查结果
     * @param request LLM安全请求对象，包含需要进行风险检查的信息
     * @return 风险检查结果，包含检查的详细信息和风险评估
     */
    Mono<RiskCheckResult> check(LlmSecureRequest request);

    /**
     * 执行风险检查并返回检查结果,全量返回值
     * @param request LLM安全请求对象，包含需要进行风险检查的信息
     * @return 风险检查结果，包含检查的详细信息和风险评估
     */
    Mono<ResponseMessage<List<DefenseApiResponseX>>> checkWithAllResp(LlmSecureRequest request);
}
