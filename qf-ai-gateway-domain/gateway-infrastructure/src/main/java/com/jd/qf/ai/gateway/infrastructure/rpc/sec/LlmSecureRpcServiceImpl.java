package com.jd.qf.ai.gateway.infrastructure.rpc.sec;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jd.qf.ai.gateway.infrastructure.rpc.sec.dto.LlmSecureRequest;
import com.jd.security.llmsec.core.BusinessConf;
import com.jd.security.llmsec.core.ResponseMessage;
import com.jd.security.llmsec.core.api.defense.DefenseApiRequest;
import com.jd.security.llmsec.core.api.defense.DefenseApiResponse;
import com.jd.security.llmsec.core.api.defense.DefenseApiResponseX;
import com.jd.security.llmsec.core.check.RiskCheckResult;
import com.jd.security.llmsec.core.session.BusinessType;
import com.jd.security.llmsec.core.session.MessageInfo;
import com.jd.security.llmsec.core.session.ResponseMode;
import com.jd.security.llmsec.core.session.Role;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.List;
import java.util.UUID;

/**
 * 大模型安全RPC服务(言安)
 * <AUTHOR>
 * @description
 * @date 2025/3/31
 */
@Slf4j
@Service
public class LlmSecureRpcServiceImpl implements LlmSecureRpcService{

    @Value("${llm.sec.url}")
    private String llmSecUrl;

    @Value("${llm.sec.accessKey}")
    private String accessKey;

    @Value("${llm.sec.secretKey}")
    private String secretKey;

    /**
     * web客户端
     */
    private final WebClient webClient;

    public LlmSecureRpcServiceImpl(@Value("${llm.sec.url}") String baseUrl) {
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .clientConnector(new ReactorClientHttpConnector(HttpClient.create()
                        .responseTimeout(Duration.ofSeconds(5))
                ))
                .build();
    }

    @Override
    public Mono<RiskCheckResult> check(LlmSecureRequest request) {
        return this.checkWithAllResp(request)
                .flatMap(res -> {
                    DefenseApiResponse defenseApiResponse = res.getData().get(0).toResponse();
                    return Mono.just(defenseApiResponse.getRiskCheckResult());
                });
    }

    @Override
    public Mono<ResponseMessage<List<DefenseApiResponseX>>> checkWithAllResp(LlmSecureRequest request) {
        log.debug("开始执行大模型安全检查,用户参数为{}", JSON.toJSONString(request));
        DefenseApiRequest defenseApiRequest = buildReq(request);
        String params = JSON.toJSONString(defenseApiRequest);
        log.debug("大模型安全检查请求RPC参数为{}", params);

        ParameterizedTypeReference<ResponseMessage<List<DefenseApiResponseX>>> typeRef =
                new ParameterizedTypeReference<>() {
                };

        return webClient.post()
                .uri(accessKey)
                .header("Content-Type", "application/json")
                .bodyValue(defenseApiRequest)
                .retrieve()
                .bodyToMono(typeRef)
                .flatMap(res -> {
                    if (res == null || !res.success()) {
                        log.error("大模型安全检查调用失败, 响应结果: {}", JSON.toJSONString(res));
                        return Mono.empty();
                    }
                    log.info("大模型安全检查返回结果为{}", JSON.toJSONString(res));

                    if (res.getData() == null || res.getData().isEmpty()) {
                        log.error("大模型安全检查返回数据为空");
                        return Mono.empty();
                    }
                    return Mono.just(res);
                })
                .onErrorResume(e -> {
                    log.error("大模型安全检查RPC异常", e);
                    return Mono.empty();
                });
    }

    public DefenseApiRequest buildReq(LlmSecureRequest req) {

        BusinessConf authConf = new BusinessConf();
        authConf.setAccessKey(accessKey);
        authConf.setSecretKey(secretKey);

        MessageInfo messageInfo = MessageInfo.messageInfoBuilder()
                .sessionId(req.getConversionId())
                .fromRole(req.getRole())
                .toRole(Role.user)
                .ext(new JSONObject()).build();

        DefenseApiRequest request = DefenseApiRequest.builder()
                .timestamp(System.currentTimeMillis())
                .requestId(UUID.randomUUID().toString())
                .accessKey(accessKey)
                .accessTarget("defenseV2")
                .content(req.getInput())
                .messageInfo(messageInfo)
                .businessType(BusinessType.toB)
                .responseMode(ResponseMode.sync)
                .build();

        request.sign(authConf);
        return request;
    }
}
