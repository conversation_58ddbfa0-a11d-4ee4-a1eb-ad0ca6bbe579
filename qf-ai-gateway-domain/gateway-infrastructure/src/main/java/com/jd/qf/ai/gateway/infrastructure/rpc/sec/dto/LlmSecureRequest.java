package com.jd.qf.ai.gateway.infrastructure.rpc.sec.dto;

import com.jd.security.llmsec.core.session.Role;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 大模型安全请求
 * <AUTHOR>
 * @description
 * @date 2025/3/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LlmSecureRequest {

    /**
     * 待检测输入
     */
    private String input;

    /**
     * 对话ID,可以检测连续的对话
     */
    private String conversionId;

    /**
     * 角色
     */
    private Role role;
}
