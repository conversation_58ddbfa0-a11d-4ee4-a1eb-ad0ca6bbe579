<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.wdy</groupId>
        <artifactId>qf-ai-gateway-domain</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>gateway-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>qf-ai-gateway-common-pojo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.security.llmsec</groupId>
            <artifactId>llm-sec-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jim.cli</groupId>
            <artifactId>jim-cli-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.jr.aks</groupId>
            <artifactId>aks-spring-config-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd</groupId>
                    <artifactId>jsf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wangyin.plat-arch</groupId>
            <artifactId>wyaks-server-api</artifactId>
        </dependency>
        <!-- oss -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>

        <!-- JMQ -->
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-spring</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.ump</groupId>
                    <artifactId>profiler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- ump接入 -->
        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
        </dependency>

        <!--            ducc依赖-->
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <version>${ducc.client.version}</version>
            <type>pom</type> <!-- type 不能省略 -->
        </dependency>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-logging</artifactId>
            <version>${ducc.client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.2</version>
        </dependency>
    </dependencies>

</project>
