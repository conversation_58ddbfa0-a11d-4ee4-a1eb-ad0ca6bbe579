package com.jd.qf.ai.gateway.core.api.chat;


import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionChunk;
import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionRequest;
import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionResponse;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * OpenAI聊天服务接口
 * 基于WebFlux的响应式编程
 * 
 * <AUTHOR>
 * @date 2025/7/15
 */
public interface OpenAIChatService {
    /**
     * 创建非流式聊天完成
     * 
     * @param request 聊天完成请求
     * @return 聊天完成响应
     */
    Mono<ChatCompletionResponse> createNonStreamingChatCompletion(ChatCompletionRequest request);

    /**
     * 创建流式聊天完成
     * 
     * @param request 聊天完成请求
     * @return 聊天完成流式响应
     */
    Flux<ChatCompletionChunk> createStreamingChatCompletion(ChatCompletionRequest request);
}
