package com.jd.qf.ai.gateway.core.api.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI Chat Completion 响应模型
 * 与OpenAI官方API保持一致
 * 
 * <AUTHOR>
 * @date 2025/7/15
 */
@Data
public class ChatCompletionResponse {

    /**
     * 响应ID
     */
    private String id;

    /**
     * 对象类型
     */
    private String object;

    /**
     * 创建时间戳
     */
    private Long created;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 系统指纹
     */
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;

    /**
     * 选择列表
     */
    private List<Choice> choices;

    /**
     * 使用情况
     */
    private Usage usage;

    /**
     * 选择项
     */
    @Data
    public static class Choice {
        /**
         * 索引
         */
        private Long index;

        /**
         * 消息
         */
        private ChatCompletionRequest.ChatMessage message;

        /**
         * 增量（流式响应使用）
         */
        private Delta delta;

        /**
         * logprobs
         */
        private Object logprobs;

        /**
         * 完成原因
         */
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    /**
     * 增量消息（流式响应使用）
     */
    @Data
    public static class Delta {
        /**
         * 角色
         */
        private String role;

        /**
         * 内容
         */
        private String content;

        /**
         * 工具调用
         */
        @JsonProperty("tool_calls")
        private List<ChatCompletionRequest.ToolCall> toolCalls;
    }

    /**
     * 使用情况
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Usage {
        /**
         * 提示token数
         */
        @JsonProperty("prompt_tokens")
        private Long promptTokens;

        /**
         * 完成token数
         */
        @JsonProperty("completion_tokens")
        private Long completionTokens;

        /**
         * 总token数
         */
        @JsonProperty("total_tokens")
        private Long totalTokens;

        /**
         * 完成token详情
         */
        @JsonProperty("completion_tokens_details")
        private CompletionTokensDetails completionTokensDetails;

        /**
         * 提示token详情
         */
        @JsonProperty("prompt_tokens_details")
        private PromptTokensDetails promptTokensDetails;

        @Data
        public static class CompletionTokensDetails {
            /**
             * 推理token数
             */
            @JsonProperty("reasoning_tokens")
            private Integer reasoningTokens;
        }

        @Data
        public static class PromptTokensDetails {
            /**
             * 缓存token数
             */
            @JsonProperty("cached_tokens")
            private Integer cachedTokens;
        }
    }
}
