package com.jd.qf.ai.gateway.core.api.chat.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * OpenAI Chat Completion 请求模型
 * 与OpenAI官方API保持一致
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Data
public class ChatCompletionRequest {

    /**
     * 消息列表
     */
    @Valid
    private List<ChatMessage> messages;

    /**
     * 模型名称
     */
    @NotEmpty(message = "模型名称不能为空")
    private String model;

    /**
     * 是否流式返回
     */
    private Boolean stream = false;

    /**
     * 最大token数
     */
    @JSONField(name = "max_tokens")
    private Integer maxTokens;

    /**
     * 温度参数，控制随机性
     */
    private Double temperature;

    /**
     * top_p参数
     */
    @JSONField(name = "top_p")
    private Double topP;

    /**
     * 生成的选择数量
     */
    private Integer n = 1;

    /**
     * 停止序列
     */
    private List<String> stop;

    /**
     * 存在惩罚
     */
    @JSONField(name = "presence_penalty")
    private Double presencePenalty;

    /**
     * 频率惩罚
     */
    @JSONField(name = "frequency_penalty")
    private Double frequencyPenalty;

    /**
     * logit偏差
     */
    @JSONField(name = "logit_bias")
    private Map<String, Double> logitBias;

    /**
     * 用户标识
     */
    private String user;

    /**
     * 工具列表
     */
    private List<Tool> tools;

    /**
     * 工具选择
     */
    @JSONField(name = "tool_choice")
    private Object toolChoice;

    /**
     * 响应格式
     */
    @JSONField(name = "response_format")
    private ResponseFormat responseFormat;

    /**
     * 种子
     */
    private Integer seed;

    /**
     * 流式选项
     */
    @JSONField(name = "stream_options")
    private StreamOptions streamOptions;

    /**
     * 流式选项
     */
    @Data
    public static class StreamOptions {
        /**
         * 是否在流式响应中包含使用统计信息
         */
        @JSONField(name = "include_usage")
        private Boolean includeUsage;
    }

    /**
     * 聊天消息
     */
    @Data
    public static class ChatMessage {
        /**
         * 角色：system, user, assistant, tool
         */
        @NotEmpty(message = "角色不能为空")
        private String role;

        /**
         * 消息内容
         */
        private String content;

        /**
         * 工具调用
         */
        @JSONField(name = "tool_calls")
        private List<ToolCall> toolCalls;

        /**
         * 工具调用ID
         */
        @JSONField(name = "tool_call_id")
        private String toolCallId;

        /**
         * 名称（function角色使用）
         */
        private String name;
    }

    /**
     * 工具调用
     */
    @Data
    public static class ToolCall {
        /**
         * ID
         */
        private String id;

        /**
         * 类型
         */
        private String type;

        /**
         * 函数调用
         */
        private Function function;

        @Data
        public static class Function {
            /**
             * 函数名
             */
            private String name;

            /**
             * 参数
             */
            private String arguments;
        }
    }

    /**
     * 工具定义
     */
    @Data
    public static class Tool {
        /**
         * 类型
         */
        private String type;

        /**
         * 函数定义
         */
        private Function function;

        @Data
        public static class Function {
            /**
             * 函数名
             */
            private String name;

            /**
             * 描述
             */
            private String description;

            /**
             * 参数schema
             */
            private Map<String, Object> parameters;
        }
    }

    /**
     * 响应格式
     */
    @Data
    public static class ResponseFormat {
        /**
         * 类型：text, json_object
         */
        private String type;
    }
}
