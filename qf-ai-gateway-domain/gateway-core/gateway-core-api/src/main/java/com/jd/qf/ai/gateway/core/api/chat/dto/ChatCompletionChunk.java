package com.jd.qf.ai.gateway.core.api.chat.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OpenAI Chat Completion 流式响应块
 * 与OpenAI官方API保持一致
 * 
 * <AUTHOR>
 * @date 2025/7/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatCompletionChunk {

    /**
     * 响应ID
     */
    private String id;

    /**
     * 对象类型
     */
    private String object;

    /**
     * 创建时间戳
     */
    private Long created;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 系统指纹
     */
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;

    /**
     * 选择列表
     */
    private List<Choice> choices;

    /**
     * 使用情况（最后一个chunk包含）
     */
    private ChatCompletionResponse.Usage usage;

    /**
     * 选择项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Choice {
        /**
         * 索引
         */
        private Long index;

        /**
         * 增量
         */
        private Delta delta;

        /**
         * logprobs
         */
        private Object logprobs;

        /**
         * 完成原因
         */
        @JsonProperty("finish_reason")
        private String finishReason;
    }

    /**
     * 增量消息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Delta {
        /**
         * 角色
         */
        private String role;

        /**
         * 内容
         */
        private String content;

        /**
         * 工具调用
         */
        @JsonProperty("tool_calls")
        private List<ToolCallDelta> toolCalls;
    }

    /**
     * 工具调用增量
     */
    @Data
    public static class ToolCallDelta {
        /**
         * 索引
         */
        private Integer index;

        /**
         * ID
         */
        private String id;

        /**
         * 类型
         */
        private String type;

        /**
         * 函数调用
         */
        private FunctionDelta function;

        @Data
        public static class FunctionDelta {
            /**
             * 函数名
             */
            private String name;

            /**
             * 参数
             */
            private String arguments;
        }
    }
}
