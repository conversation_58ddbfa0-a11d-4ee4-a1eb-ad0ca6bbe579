<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.wdy</groupId>
        <artifactId>qf-ai-gateway-domain</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>gateway-core</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>gateway-core-api</module>
        <module>gateway-core-service</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>gateway-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>qf-ai-gateway-common-lang</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>gateway-common</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.source}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>