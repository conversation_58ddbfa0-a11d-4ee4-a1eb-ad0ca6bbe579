package com.jd.qf.ai.gateway.core.service.lb;

import cn.hutool.core.collection.CollectionUtil;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.gateway.core.api.lb.LoadBalanceService;
import com.jdt.open.capability.log.annotation.NoLog;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 负载均衡服务实现
 *
 * <AUTHOR>
 * @description
 * @date 2025/7/16
 */
@Service
@Slf4j
public class LoadBalanceServiceImpl implements LoadBalanceService {

    @LafValue("loadBalanceConfig")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, List<String>> loadBalanceConfigMap = new HashMap<>();

    /**
     * 轮询计数器
     */
    Map<String, AtomicInteger> counterMap = new HashMap<>();

    /**
     * 轮询算法实现的apikey路由
     */
    @Override
    @NoLog
    public String getRouterBaseUrl(String modelType) {

        AtomicInteger counter = counterMap.computeIfAbsent(modelType, k -> new AtomicInteger(0));
        if (counter.get() == Integer.MAX_VALUE) {
            counter.set(0);
        }
        List<String> urlList = loadBalanceConfigMap.get(modelType);
        if (CollectionUtil.isEmpty(urlList)) {
            log.error("未找到负载均衡配置,入参为{}", modelType);
            throw new BizException("未找到负载均衡配置");
        }
        int index = counter.getAndIncrement() % urlList.size();
        counterMap.put(modelType, counter);
        return urlList.get(index);
    }
}
