package com.jd.qf.ai.gateway.core.service.chat;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jd.qf.ai.gateway.common.enums.ModelTypeEnum;
import com.jd.qf.ai.gateway.core.api.chat.OpenAIChatService;
import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionChunk;
import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionRequest;
import com.jd.qf.ai.gateway.core.api.chat.dto.ChatCompletionResponse;

import com.jd.qf.ai.gateway.core.api.lb.LoadBalanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.atomic.AtomicReference;

/**
 * OpenAI聊天服务实现
 * 基于WebFlux的响应式编程
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@Service
public class OpenAIChatServiceImpl implements OpenAIChatService {

    @Autowired
    @Qualifier("openaiWebClient")
    private WebClient webClient;
    @Autowired
    private LoadBalanceService loadBalanceService;

    @Override
    public Mono<ChatCompletionResponse> createNonStreamingChatCompletion(ChatCompletionRequest request) {

        // 确保stream为false
        request.setStream(false);

        //获取路由url
        String routerBaseUrl = loadBalanceService.getRouterBaseUrl(request.getModel());
        log.info("开始创建非流式聊天完成，请求: {},路由地址: {}", JSON.toJSONString(request), routerBaseUrl);
        return webClient.post()
                .uri(routerBaseUrl + "/chat/completions")
                .contentType(MediaType.APPLICATION_JSON)
                //使用fastjson而不用jackson序列化方式,兼容更多的下游框架
                .body(BodyInserters.fromValue(JSON.toJSONString(request)))
                .retrieve()
                .onStatus(HttpStatus::isError, response -> {
                    // 读取响应体并打印日志
                    return response.bodyToMono(String.class)
                            .doOnNext(body -> log.error("收到HTTP错误响应，状态码: {}, 响应体: {}", response.statusCode(), body))
                            .then(Mono.error(new RuntimeException("非流式聊天完成失败,状态码: " + response.statusCode())));
                })
                .bodyToMono(ChatCompletionResponse.class)
                .doOnSuccess(response -> log.info("非流式聊天完成成功，响应: {}", JSON.toJSONString(response.getChoices().get(0).getMessage())))
                .doOnError(error -> log.error("非流式聊天完成失败", error));
    }

    @Override
    public Flux<ChatCompletionChunk> createStreamingChatCompletion(ChatCompletionRequest request) {

        // 确保stream为true
        request.setStream(true);
        String routerBaseUrl = loadBalanceService.getRouterBaseUrl(request.getModel());
        log.info("开始创建流式聊天完成，请求: {},路由地址: {}", JSON.toJSONString(request), routerBaseUrl);
        AtomicReference<StringBuilder> fullResponse = new AtomicReference<>(new StringBuilder());

        return webClient.post()
                .uri(routerBaseUrl + "/chat/completions")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .header("Accept-Charset", "UTF-8")
                .body(BodyInserters.fromValue(JSON.toJSONString(request)))
                .retrieve()
                .onStatus(HttpStatus::isError, response -> {
                    // 读取响应体并打印日志
                    return response.bodyToMono(String.class)
                            .doOnNext(body -> log.error("流式请求收到HTTP错误响应，状态码: {}, 响应体: {}", response.statusCode(), body))
                            .then(Mono.error(new RuntimeException("非流式聊天完成失败,状态码: " + response.statusCode())));
                })
                .bodyToFlux(String.class)
                //过滤掉回复完成,方便统一反序列化成自己的适配格式
                .filter(line -> !line.startsWith("[DONE]"))
                .map(line -> {
                    log.debug("收到大模型回复原始报文: {}", line);
                    return JSON.parseObject(line, ChatCompletionChunk.class);
                })
                .doOnNext(response -> {
                    log.debug("收到部分大模型回复{}", JSON.toJSONString(response));
                    if (response != null && response.getChoices().get(0).getDelta().getContent() != null) {
                        String content = response.getChoices().get(0).getDelta().getContent();
                        fullResponse.get().append(content);
                    }
                })
                .doOnComplete(() -> {
                    String completeResponse = fullResponse.get().toString();
                    log.info("收到完整大模型回复:\n{}", completeResponse);
                })
                .doOnError(error -> log.error("流式聊天完成失败", error));
    }
}
