package com.jd.qf.ai.gateway.core.service.jdq;

import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * JDQ消息服务
 *
 * <AUTHOR>
 * @description
 * @date 2025/3/31
 */
@Service
@Slf4j
@Profile({"local","pre","prod"})
public class JdqProducerService {

    @Autowired
    private Map<String, KafkaProducer<String, String>> jdqProducerFactory;

    public void sendMessage(String topic, String key, String value) {
        KafkaProducer<String, String> producer = jdqProducerFactory.get(topic);
        if (producer == null) {
            log.error("未找到JDQ生产者配置,topic:{}", topic);
            throw new BizException("未找到JDQ生产者配置,topic:" + topic);
        }
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, key, value);

        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                log.error("发送消息失败，{}", exception.getMessage());
            } else {
                log.info("消息发送成功，topic:{},key:{},value:{}", topic, key, value);
            }
        });
        producer.flush();
    }
}
