package com.jd.qf.ai.gateway.core.service.auth;

import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.gateway.core.api.auth.AuthService;
import com.jd.qf.ai.gateway.core.api.auth.dto.AuthReq;
import com.jdt.open.capability.log.annotation.NoLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 鉴权服务实现
 * <AUTHOR>
 * @description
 * @date 2025/7/17
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    /**
     * 鉴权配置,key是apikey,value是应用名
     * 生成逻辑:sk-{QF-AI-{APP_NAME}的md5}
     */
    @LafValue("authConfig")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, String> authConfigMap = new HashMap<>();

    @Override
    @NoLog
    public boolean auth(AuthReq authReq) {
        return authConfigMap.containsKey(authReq.getApiKey());
    }
}
