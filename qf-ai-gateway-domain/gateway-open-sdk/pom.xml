<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jd.wdy</groupId>
        <artifactId>qf-ai-gateway-domain</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>gateway-open-sdk</artifactId>
    <version>1.0.3-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>qf-ai-gateway-common-pojo</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>Release Repository</name>
            <url>https://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>JD maven2 repository-snapshots</name>
            <url>https://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>

</project>