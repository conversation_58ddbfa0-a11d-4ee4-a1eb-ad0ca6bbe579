package com.jd.qf.ai.gateway.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Agent类型
 * <AUTHOR>
 * @date 2024-12-25 16:23
 */
@Getter
@AllArgsConstructor
public enum AgentTypeEnum {
    /**
     * Dify
     */
    DIFY("DIFY", "DIFY"),
    /**
     * 言犀
     */
    CHAT_RHINO("CHAT_RHINO", "言犀"),
    /**
     * OpenAI
     */
    OPENAI("OPENAI", "OpenAI")
    ;

    /**
     * CODE
     */
    private final String code;
    /**
     * MSG
     */
    private final String msg;

    public static String getMsgByCode(String code) {
        for (AgentTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getMsg();
            }
        }
        return null;
    }
}
