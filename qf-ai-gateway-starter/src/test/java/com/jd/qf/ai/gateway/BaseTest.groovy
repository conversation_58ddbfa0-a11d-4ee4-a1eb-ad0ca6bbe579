package com.jd.qf.ai.gateway

import com.jd.qf.ai.gateway.starter.Application
import com.jdt.open.capability.config.InitRegister
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.transaction.annotation.Transactional
import spock.lang.Specification

@SpringBootTest(classes = Application.class, properties = [
        "spring.datasource.url=jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;MODE=MySQL",
        "spring.datasource.driver-class-name=org.h2.Driver",
        "spring.sql.init.platform= h2",
        "spring.sql.init.schema-locations=classpath:h2/schema.sql",
        "spring.sql.init.data-locations=classpath:h2/init/*.sql",
        "spring.sql.init.mode=always",
        "open.health.enable=false",
        "open.validator.enable=true",
        "open.auto.cache.enable=false",
        "spring.datasource.username=root",
        "spring.jmq.enabled=false",
        "jsf.gateway.port=-1",
        "logging.level.com.jdt.open.capability.encrypt=OFF",
        "jdd.easyjob.enabled=false",
        "pro.jimdb.url=jim://2914173422341158041/110000259"
], webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Transactional
@ActiveProfiles(["local"])
@DirtiesContext
@EnableAutoConfiguration(exclude = [InitRegister.class])
class BaseTest extends Specification {


    def setupSpec() {
        println "单元测试setupSpec执行开始"

    }

    def cleanupSpec() {
        println "单元测试afterAll执行结束"
    }
}
