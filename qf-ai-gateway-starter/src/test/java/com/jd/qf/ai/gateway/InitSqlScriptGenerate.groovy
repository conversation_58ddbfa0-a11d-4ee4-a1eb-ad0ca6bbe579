package com.jd.qf.ai.gateway

/**
 * 初始化sql脚本生成
 */
class InitSqlScriptGenerate {
//    /**
//     * 生成sql
//     */
    static List<String> generate() {
        def sqlArr = []
//        //业务sql
//        def noticeSql = noticeMessageGenerate()
//        sqlArr.addAll(noticeSql)
        return sqlArr
    }

//    static def noticeMessageGenerate() {
//        def sqlArr = []
//        for (int i = 1; i < 28; i++) {
//            sqlArr.add("insert into notice_message ( message_no, title, `type`, content ) values ( '" + i + "', '测试消息', " + NoticeMessageTypeEnum.ADD_EMPLOYEE.code + ",'测试消息' );")
//            for (int j = 1; j < 28; j++) {
//                sqlArr.add("insert into notice_record ( notice_no, message_no, recipient_no, `status`, recipient_type ) values ( '" + i+""+j + "', '" + i + "', '12345', " + NoticeStatusEnum.values()[j % NoticeStatusEnum.values().length].code + ", " + NoticeRecipientTypeEnum.values()[j % NoticeRecipientTypeEnum.values().length].code + " );")
//            }
//        }
//        return sqlArr
//    }

}
