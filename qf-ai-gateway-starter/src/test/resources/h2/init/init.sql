-- AI聊天记录表测试数据

INSERT INTO ai_chat_record (
  record_no, corp_id, project_id, project_name, msg_record_id,msg_time, sys_user_id, sys_user_name, cust_id, cust_name, msg_text, ai_answer, msg_context, data_status, accept_status, no_apply_reason, valid, creator, modifier, created_time, modified_time
) VALUES
('REC20240601001', 'CORP001', 'PROJ001', '智能客服项目', 'MSG20240601001', CURRENT_TIMESTAMP, 1001, '张三', 'CUST001', '李四', '你好，AI，请帮我查询订单状态。', '您的订单已发货，预计明天送达。', '{"orderId":"ORD123456","queryType":"status"}', 'COMPLETED', 'ACCEPTED', NULL, 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('REC20240601002', 'CORP002', 'PROJ002', '营销机器人', 'MSG20240601002','2025-01-19 15:53:20', 1002, '李雷', 'CUST002', '王五', '请推荐一款适合我的产品。', '推荐您试试我们的智能手表。', '{"recommend":["智能手表","蓝牙耳机"]}', 'COMPLETED', 'NOT_ACCEPTED', '推荐不准确', 1, 'admin', '李雷', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('REC20240601003', 'CORP003', NULL, NULL, 'MSG20240601003','2025-01-18 15:53:22', 1003, '王芳', 'CUST003', '赵六', '请问你们的售后电话是多少？', '您好，售后电话是400-800-8888。', '{"service":"after-sale"}', 'COMPLETED', 'ACCEPTED', NULL, 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('REC20240601004', 'CORP001', 'PROJ001', '智能客服项目', 'MSG20240601004','2025-01-18 15:53:24', 1004, '赵强', 'CUST004', '孙七', '请帮我取消订单。', NULL, '{"orderId":"ORD654321","action":"cancel"}', 'COMPLETED', 'ACCEPTED', '订单已发货，无法取消', 1, 'system', '赵强', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('REC20240601005', 'CORP002', 'PROJ003', 'AI助手', 'MSG20240601005','2025-01-18 15:53:40', 1005, '钱进', 'CUST005', '周八', '请问今天的天气如何？', '今天天气晴，气温25℃。', '{"city":"北京","date":"2024-06-01"}', 'PROCESSING', 'OPPOSED', 'AI回复不准确', 1, 'admin', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('REC20240601006', 'CORP003', NULL, NULL, 'MSG20240601006','2025-01-18 15:53:48', 1006, '孙丽', 'CUST006', '吴九', '请帮我查下快递进度。', '您的快递已到达北京分拨中心。', '{"expressNo":"SF1234567890"}', 'COMPLETED', 'ACCEPTED', NULL, 1, 'system', '孙丽', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- OA项目表测试数据
INSERT INTO oa_project (
  corp_id, project_code, project_name, contact, contact_phone, status, fail_msg, master_id, master_name, audit_time, audit_user, version, is_valid, creator, modifier, created_date, modified_date, customer_id, customer_name, depart_id, start_date, end_date
) VALUES
('CORP001', 'PRJ20240601', '智能客服系统项目', '张经理', '13800138001', 1, NULL, 'ORG001', '京东科技', '2024-06-01 10:00:00', 'admin', 1, true, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CUST001', '京东商城', 'DEPT001', '2024-06-01', '2024-12-31'),
('CORP001', 'PROJ001', '智能客服系统项目02', '张经理', '13800138001', 1, NULL, 'ORG001', '京东科技', '2024-06-01 10:00:00', 'admin', 1, true, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CUST001', '京东商城', 'DEPT001', '2024-06-01', '2024-12-31'),
('CORP001', 'PROJ002', '智能客服系统项目03', '张经理', '13800138001', 1, NULL, 'ORG001', '京东科技', '2024-06-01 10:00:00', 'admin', 1, true, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'CUST001', '京东商城', 'DEPT001', '2024-06-01', '2024-12-31');

-- 系统用户表测试数据
INSERT INTO sys_user (
  corp_id, login_mobile, login_pin, user_name, sex, user_type, is_mobile_valid, head_img_url, is_agree, agree_time, create_time, last_login_time, pwd_update_time, upper_user_id, depart_id, status, wx_open_id, ww_user_id, rank_type, department, is_activate, project_id, project_name, customer_id, customer_name
) VALUES
('CORP001', '13800138001', 'zhangsan', '张三', '男', '1', 1, 'https://example.com/avatar.jpg', 1, '2024-01-01 08:00:00', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2024-01-01 08:00:00', 'USER1000', 1, 1, 'wx123456', 'ww123456', 'MANAGER', 'IT部门', 1, 'PROJ001', '智能客服项目', 'CUST001', '京东商城');

-- 客户表测试数据
INSERT INTO cust (
  id, wx_cust_user_id,data_type, name, sex, mobile, email, province, city, county, company, source, corp_id, status, project_id, lifecycle, position, create_time, update_time, created_date, modified_date
) VALUES
('CUST001','wx_cust_001', 'CUST', '李四', '男', '13900139001', '<EMAIL>', '北京', '北京', '朝阳区', '京东商城', '企业', 'CORP001', 1, 'PROJ001', '付款用户', '经理', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


-- 客服聊天记录表测试数据
INSERT INTO cs_chat_record_new (id,
  cs_id, robot_id, wx_cust_id, wx_group_id, wx_user_id, cust_id, corp_id, msg_type, msg_json, chat_type, fans_type, send_time, receive_time, status, chat_id, msg_id, msg_send_status, sha, is_analysed_sentiment, is_top, un_read, is_delete, recall
) VALUES
-- 客户发送给员工的消息，与 sys_user 和 cust 表关联
(1,1, 1, 'wx_cust_001', NULL, 'ww123456', 'CUST001', 'CORP001', 'text', '{"content":"您好，我想咨询一下产品的使用问题。"}', 'receive', 'FANS', '2025-05-01 09:30:00', '2025-05-01 09:30:00', 1, 'chat_001', 'msg_001', 2, 'sha_001', 2, 0, 0, 0, 0),

-- 员工回复客户的消息，与 sys_user 和 cust 表关联
(2,1, 1, 'wx_cust_001', NULL, 'ww123456', 'CUST001', 'CORP001', 'text', '{"content":"您好，非常高兴为您服务。请问是哪款产品遇到了问题？"}', 'send', 'FANS', '2025-05-01 09:30:00', '2025-05-01 09:30:00', 1, 'chat_001', 'msg_002', 2, 'sha_002', 2, 0, 0, 0, 0),

-- 客户发送图片消息，与 sys_user 和 cust 表关联
(3,1, 1, 'wx_cust_001', NULL, 'ww123456', 'CUST001', 'CORP001', 'text', '{"media_id":"media_001","pic_url":"https://example.com/images/product_issue.jpg"}', 'receive', 'FANS', '2025-05-01 09:30:00', '2025-05-01 09:30:00', 1, 'chat_001', 'msg_003', 2, 'sha_003', 0, 0, 0, 0, 0),

-- 员工发送链接消息，与 sys_user 和 cust 表关联
(4,1, 1, 'wx_cust_001', NULL, 'ww123456', 'CUST001', 'CORP001', 'text', '{"title":"产品使用手册","description":"详细的产品使用说明和常见问题解答","url":"https://example.com/product/manual","thumb_url":"https://example.com/images/manual_thumb.jpg"}', 'send', 'FANS', '2025-05-01 09:30:00', '2025-05-01 09:30:00', 1, 'chat_001', 'msg_004', 2, 'sha_004', 0, 0, 0, 0, 0),

-- 客户发送感谢消息，与 sys_user 和 cust 表关联
(5,1, 1, 'wx_cust_001', NULL, 'ww123456', 'CUST001', 'CORP001', 'text', '{"content":"非常感谢您的帮助！我已经解决了问题。"}', 'receive', 'FANS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 'chat_001', 'msg_005', 2, 'sha_005', 3, 0, 0, 0, 0);

-- 知识库分组表测试数据
INSERT INTO ai_know_group (
  project_id, project_name, group_no, group_name, group_type, valid, creator, modifier, created_time, modified_time
) VALUES
('PROJ001', '智能客服项目', 'GROUP001', '默认分组', 'DEFAULT', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001', '智能客服项目', 'GROUP002', '常见问题', 'NORMAL', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001', '智能客服项目', 'GROUP003', '产品介绍', 'NORMAL', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ002', '营销机器人', 'GROUP004', '默认分组', 'DEFAULT', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ002', '营销机器人', 'GROUP005', '活动规则', 'NORMAL', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 知识库配置表测试数据
INSERT INTO ai_know_config (
  config_no, project_id, project_name, know_type, content_type, external_know_id, valid, creator, modifier, created_time, modified_time
) VALUES
('CONFIG001', 'PROJ001', '智能客服项目', 'DIFY', 'QA', 'DIFY_KNOW_001', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('CONFIG002', 'PROJ001', '智能客服项目', 'DIFY', 'QUESTION', 'DIFY_KNOW_002', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('CONFIG003', 'PROJ002', '营销机器人', 'DIFY', 'QA', 'DIFY_KNOW_003', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('CONFIG004', 'PROJ003', 'AI助手', 'DIFY', 'QA', 'DIFY_KNOW_004', 1, 'admin', 'admin', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('CONFIG005', 'PROJ001', '智能客服项目', 'DIFY', 'QUESTION', 'DIFY_KNOW_005', 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 知识库问答表测试数据
INSERT INTO ai_know_qa (
    group_no, qa_no, project_id, project_name, external_question_id, external_qa_id, question, question_md5, answer, valid, creator, modifier, created_time, modified_time
) VALUES
      ('GROUP001', 'QA001', 'PROJ001', '智能客服项目', 'EXT_Q001', 'EXT_QA001', '如何使用智能客服系统？', 'b6ffbc7f47efd27db49f414c1eb8f725', '智能客服系统使用非常简单，您只需要登录系统后台，配置好知识库和问答规则，然后将系统接入您的客服渠道即可开始使用。', 1, 'system', 'system', '2025-01-01 10:00:00', '2025-01-01 10:00:00'),
      ('GROUP002', 'QA002', 'PROJ001', '智能客服项目', 'EXT_Q002', 'EXT_QA002', '系统支持哪些接入方式？', 'hash_002', '系统支持多种接入方式：1. 网页聊天窗口；2. 微信公众号；3. 企业微信；4. API接口；5. 小程序。', 1, 'system', 'system', '2025-01-01 10:00:00', '2025-01-01 10:00:00'),
      ('GROUP002', 'QA003', 'PROJ001', '智能客服项目', 'EXT_Q003', 'EXT_QA003', '如何训练AI模型？', 'hash_003', 'AI模型训练需要：1. 准备高质量的问答数据；2. 在知识库管理中上传训练数据；3. 设置训练参数；4. 启动训练任务；5. 评估模型效果。', 1, 'admin', 'admin', '2025-01-01 10:00:00', '2025-01-01 10:00:00'),
      ('GROUP003', 'QA004', 'PROJ001', '智能客服项目', 'EXT_Q004', 'EXT_QA004', '产品有哪些核心功能？', 'hash_004', '核心功能包括：1. 智能问答；2. 多轮对话；3. 意图识别；4. 知识库管理；5. 数据统计分析；6. 人工客服转接。', 1, 'system', NULL, '2025-01-02 10:00:00', CURRENT_TIMESTAMP),
      ('GROUP004', 'QA005', 'PROJ002', '营销机器人', 'EXT_Q005', 'EXT_QA005', '营销机器人如何设置营销话术？', 'hash_005', '设置营销话术步骤：1. 进入话术管理页面；2. 创建话术模板；3. 设置触发条件；4. 配置回复内容；5. 测试并发布。', 1, 'system', 'system', '2025-01-02 10:00:00', CURRENT_TIMESTAMP),
      ('GROUP005', 'QA006', 'PROJ002', '营销机器人', 'EXT_Q006', 'EXT_QA006', '活动规则如何配置？', 'hash_006', '活动规则配置包括：1. 设置活动时间；2. 定义参与条件；3. 配置奖励规则；4. 设置活动限制；5. 发布活动。', 1, 'admin', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('GROUP001', 'QA007', 'PROJ001', '智能客服项目', 'EXT_Q007', 'EXT_QA007', '系统的安全性如何保障？', 'hash_007', '系统安全保障措施：1. 数据加密传输；2. 访问权限控制；3. 操作日志记录；4. 定期安全扫描；5. 数据备份机制。', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('GROUP002', 'QA008', 'PROJ001', '智能客服项目', 'EXT_Q008', 'EXT_QA008', '如何查看系统使用统计？', 'hash_008', '查看统计数据：1. 登录管理后台；2. 进入数据统计页面；3. 选择统计时间范围；4. 查看各项指标数据；5. 导出统计报表。', 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('xxx', 'QA009', 'PROJ001', '智能客服项目', 'EXT_Q001', 'EXT_QA001', '如何使用智能客服系统？', 'hash_001', '智能客服系统使用非常简单，您只需要登录系统后台，配置好知识库和问答规则，然后将系统接入您的客服渠道即可开始使用。', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


-- 知识库相似问题表测试数据
INSERT INTO ai_know_similar_question (
    project_id,similar_question_no, qa_no, similar_question, similar_question_md5, external_question_id, external_qa_id, valid, creator, modifier, created_time, modified_time
) VALUES
-- QA001的相似问题
('PROJ001','SQ001', 'QA001', '怎么使用智能客服系统？', '575588fa4e2d8289da9c0f985abe77e8', 'EXT_SQ001', 'EXT_QA001', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ002', 'QA001', '智能客服系统如何操作？', 'hash_sq002', 'EXT_SQ002', 'EXT_QA001', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ003', 'QA001', '智能客服系统使用方法', 'hash_sq003', 'EXT_SQ003', 'EXT_QA001', 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- QA002的相似问题
('PROJ001','SQ004', 'QA002', '支持什么接入方式？', 'hash_sq004', 'EXT_SQ004', 'EXT_QA002', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ005', 'QA002', '有哪些接入渠道？', 'hash_sq005', 'EXT_SQ005', 'EXT_QA002', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ006', 'QA002', '可以通过什么方式接入？', 'hash_sq006', 'EXT_SQ006', 'EXT_QA002', 1, 'admin', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- QA003的相似问题
('PROJ001','SQ007', 'QA003', '怎样训练AI模型？', 'hash_sq007', 'EXT_SQ007', 'EXT_QA003', 1, 'admin', 'admin', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ008', 'QA003', 'AI模型训练步骤', 'hash_sq008', 'EXT_SQ008', 'EXT_QA003', 1, 'admin', 'admin', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ009', 'QA003', '如何进行模型训练？', 'hash_sq009', 'EXT_SQ009', 'EXT_QA003', 1, 'admin', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- QA004的相似问题
('PROJ001','SQ010', 'QA004', '产品功能有哪些？', 'hash_sq010', 'EXT_SQ010', 'EXT_QA004', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ011', 'QA004', '主要功能介绍', 'hash_sq011', 'EXT_SQ011', 'EXT_QA004', 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- QA005的相似问题
('PROJ001','SQ012', 'QA005', '如何配置营销话术？', 'hash_sq012', 'EXT_SQ012', 'EXT_QA005', 1, 'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ013', 'QA005', '营销话术设置方法', 'hash_sq013', 'EXT_SQ013', 'EXT_QA005', 1, 'system', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- QA006的相似问题
('PROJ001','SQ014', 'QA006', '怎么设置活动规则？', 'hash_sq014', 'EXT_SQ014', 'EXT_QA006', 1, 'admin', 'admin', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('PROJ001','SQ015', 'QA006', '活动规则设置步骤', 'hash_sq015', 'EXT_SQ015', 'EXT_QA006', 1, 'admin', NULL, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


--热门问题表测试数据
INSERT INTO ai_know_hot_question (
    hot_question_no, project_id, project_name, hot_question,
    maintain_status, question_type, similar_question,
    total_ask_count, human_ask_count, similar_ask_count,
    dt, dt_type, valid, creator, modifier,
    created_time, modified_time
) VALUES
-- 项目1(PROJ001)的日维度老问题
('HOT001', 'PROJ001', '智能客服项目', '如何重置密码？',
 'YES', 'OLD', '["怎么修改密码", "密码忘记了怎么办", "重置密码的步骤"]',
 100, 80, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

('HOT002', 'PROJ001', '智能客服项目', '账号被锁定怎么办？',
 'YES', 'OLD', '["账号解锁流程", "账号锁定解决方案", "如何解除账号锁定"]',
 90, 70, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 项目1(PROJ001)的日维度新问题
('HOT003', 'PROJ001', '智能客服项目', '新版本如何使用？',
 'NO', 'NEW', '["新功能怎么用", "新版本操作说明", "更新后使用方法"]',
 50, 40, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

('HOT004', 'PROJ001', '智能客服项目', '新功能体验反馈？',
 'NO', 'NEW', '["新版本体验如何", "新功能评价", "使用体验反馈"]',
 45, 35, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 项目2(PROJ002)的日维度老问题
('HOT005', 'PROJ002', '营销机器人', '如何创建活动？',
 'YES', 'OLD', '["怎么新建活动", "活动创建流程", "创建营销活动"]',
 150, 120, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

('HOT006', 'PROJ002', '营销机器人', '活动数据统计？',
 'YES', 'OLD', '["营销数据查看", "活动效果统计", "数据分析方法"]',
 140, 110, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 项目2(PROJ002)的日维度新问题
('HOT007', 'PROJ002', '营销机器人', '智能推荐功能使用？',
 'NO', 'NEW', '["AI推荐使用", "智能推荐设置", "推荐功能配置"]',
 60, 45, 3,
 '2025-05-01', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 项目1(PROJ001)的周维度老问题
('HOT008', 'PROJ001', '智能客服项目', '系统常见问题',
 'YES', 'OLD', '["使用常见问题", "系统问题汇总", "常见故障解决"]',
 200, 160, 3,
 '2025-05-01', 'WEEK', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

('HOT009', 'PROJ001', '智能客服项目', '系统性能优化建议',
 'YES', 'OLD', '["性能提升方法", "系统加速技巧", "优化使用体验"]',
 180, 150, 3,
 '2025-05-01', 'WEEK', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 项目1(PROJ001)的周维度新问题
('HOT010', 'PROJ001', '智能客服项目', '新版本周报告',
 'NO', 'NEW', '["本周更新内容", "周版本变更", "本周功能更新"]',
 80, 60, 3,
 '2025-05-01', 'WEEK', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 项目2(PROJ002)的周维度老问题
('HOT011', 'PROJ002', '营销机器人', '周度运营报告',
 'YES', 'OLD', '["本周运营数据", "周度效果分析", "运营周报"]',
 170, 140, 3,
 '2025-05-01', 'WEEK', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 添加一些历史数据
('HOT012', 'PROJ001', '智能客服项目', '历史问题汇总',
 'YES', 'OLD', '["往期问题", "历史记录", "问题归档"]',
 120, 100, 3,
 '2025-04-30', 'DAY', 1, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 添加一些无效数据
('HOT013', 'PROJ001', '智能客服项目', '已删除的问题',
 'YES', 'OLD', '["删除记录", "无效问题", "废弃内容"]',
 30, 20, 3,
 '2025-05-01', 'DAY', 0, 'system', null,
 CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);