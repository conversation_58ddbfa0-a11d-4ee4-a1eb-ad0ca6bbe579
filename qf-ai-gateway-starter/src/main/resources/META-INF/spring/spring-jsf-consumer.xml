<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
				http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">


    <!--aks-->
    <bean id="deviceCryptoService" class="com.wangyin.key.server.DeviceCryptoService" init-method="init"
          destroy-method="logout">
        <property name="cryptoDisService" ref="jsfcryptoDisService"/>
        <property name="appID" value="${aks.appId}"/>
        <property name="appName" value="${aks.appName}"/>
    </bean>
    <jsf:consumer id="jsfcryptoDisService"
                  interface="com.wangyin.key.server.JSFCryptoDistanceService" protocol="jsf"
                  alias="${aks.jsfAlias}" timeout="5000" retries="0" check="false"/>
</beans>