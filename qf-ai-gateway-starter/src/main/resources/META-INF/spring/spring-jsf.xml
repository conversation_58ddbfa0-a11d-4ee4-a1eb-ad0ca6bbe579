<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="${jsf.registry.index}"/>

    <jsf:server id="jsfServer" protocol="jsf"/>

    <jsf:filter id="jsfLogFilterBean" class="com.jdt.open.capability.log.JsfLogFilter" providers="" consumers="*"/>

    <import resource="spring-jsf-provider.xml"/>
    <import resource="spring-jsf-consumer.xml"/>
</beans>