<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    			http://code.jd.com/schema/jmq
				http://code.jd.com/schema/jmq/jmq-1.1.xsd">

    <!-- baseDomain start -->
    <jmq:transport id="bizDomainJmq4Transport" address="${jmq.bizDomain.address}" user="${jmq.bizDomain.user}" password="${jmq.bizDomain.password}" app="${jmq.bizDomain.app}"
                   epoll="false" sendTimeout="20000"/>
    <jmq:producer id="bizDomainProducer" retryTimes="3" transport="bizDomainJmq4Transport"/>
</beans>