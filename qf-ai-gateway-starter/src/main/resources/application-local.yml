aks:
  aliasName: pto_05_001
  appId: Test_Online
  appName: Test_Online
  jsfAlias: AKS-JSF-beta_jsf_gn
  key:
    alias:
      name: ${aks.aliasName}

jdd:
  easyjob:
    appId: qf-ai-server
    enable: true
    host: http://schedule.jdd-beta.local:8080
    secret: 455d46c86d691bed1a72f2073be05e3e

jsf:
  registry:
    index: test.i.jsf.jd.local
  provider:
    alias: local
  alias:
    jr-custinfo-api-service-exp: test

jmq:
  bizDomain:
    address: test-nameserver.jmq.jd.local:50088
    app: qfaiserver
    user: qfaiserver
    password: d5273212a770480d9ad537e74c51f5b5
  topic:
    csChatBinlog: cs_chat_record_new_binlog_test
    delayProcessAiReply: delayProcessAiReply_test
    forwardWsMessage: forward_ws_message_order_topictest



jmq2:
  deliverDomain:
    address: jmq-testcluster.jd.local:50088
    app: bpompSettle
    user: bpompSettle
    password: C653F323

ducc:
  appname: qf-ai-gateway
  configuration: test
  domain: test.ducc.jd.local
  namespace: qf-ai-gateway
  profiles: DUCC_DEFAULT_PROFILE
  token: 3ef194365fb54388b4e4a99e200af2d3


# 知识库相关配置
# Elasticsearch配置
jes:
  serverUrl: http://es-b8g7p5wl8q-http.es-b8g7p5wl8q.svc.guan-prod.gnhk.x.abchost.local:9200
  appName: wdy-cdp
  secret: 8bf858a8bddce1149c66c869e7316735

# Vearch配置
vearch:
  master:
    url: http://common-test-master.vectorbase.svc.lf09.n.jd.local
  router:
    url: http://common-test-router.vectorbase.svc.lf09.n.jd.local

# Xinference配置
xinference:
  base:
    url: http://xinference-server:9997
  embedding:
    model: bge-base-zh-v1.5
  rerank:
    model: bge-reranker-v2-m3
  token: 3ef194365fb54388b4e4a99e200af2d3
laf:
  config:
    logger:
      enabled: true
      key: logger.level
      type: logback
    manager:
      application: qf-ai-gateway
      parameters:
        - name: autoListener
          value: true
      resources:
        - name: qf-ai-gateway
          uri: ucc://${ducc.appname}:${ducc.token}@${ducc.domain}/v1/namespace/${ducc.namespace}/config/${ducc.configuration}/profiles/${ducc.profiles}?longPolling=15000&amp;necessary=true
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
open:
  act:
    log:
      app: qf-ai-gateway
      business: qf-ai-gateway
      enable: true
  lab:
    env: dev
  mybatis:
    encrypt:
      enable: true
  health:
    enable: false
oss:
  accessKey: CCA150C76F9240D0BCB6A7741A79BDBB
  bucket: mkt-crm
  endpoint: http://s3-internal.guan-test-1.jdd-beta.local
  export:
    temp:
      path: /export/temp
  secretKey: A5C01C1C4898439B961F4D6F998D3CE8
  signingRegion: guan-test-1
pro:
  jimdb:
    url: jim://2914173422341158041/110000259
server:
  port: 6080
# 压测模拟
#  tomcat:
#    threads:
#      max: 3  # 最大线程数
#      min-spare: 1  # 最小空闲线程数
#    max-connections: 8192  # 最大连接数
#    accept-count: 100  # 等待队列长度
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: c_hrms_devjzHBlV

  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB

dify:
  api:
    base-url: http://**************/v1
    timeout: 30000

chatRhino:
  api:
    base-url: http://api.chatrhino.jd.com/api/v2

llm:
  sec:
    url: http://pre-llmsec-entry.jd.local/llmsec/api/defense/v2/
    accessKey: JDR_weidianyun_001
    secretKey: fd52bdd3cac84308afcafd829831e2ee

jdqTopic:
  agentChatRecord: ai_agent_input_output

jdq:
  producerConfigs:
    ai_agent_input_output:
      user: P8cdeb189
      appDomain: ENTRY
      password: vW9qwz2XsCpha7Jb


selfBuild:
  api:
    route:
      base-url: http://localhost:8000
    chat:
      base-url: http://localhost:8000

agent:
  api:
    base-url: http://localhost:6080/qf/ai/inner

scrm:
  robotGateway:
    api:
      base-url: http://127.0.0.1:8089