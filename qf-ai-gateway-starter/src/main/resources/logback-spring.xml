<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="APP" value="qf-ai-gateway"/>

    <springProperty scope="context" name="springProfile" source="spring.profiles.active" defaultValue="local"/>
    <!-- 默认日志目录 -->
    <property name="LOG_HOME" value="${user.home}/export/log/${APP}"/>

    <!-- 开发环境日志目录 -->
    <springProfile name="local">
        <property name="LOG_HOME" value="${user.home}/export/log/${APP}"/>
    </springProfile>

    <springProfile name="dev">
        <property name="LOG_HOME" value="${user.home}/export/log/${APP}"/>
    </springProfile>

    <!-- 测试环境日志目录 -->
    <springProfile name="test">
        <property name="LOG_HOME" value="/export/log/${APP}"/>
    </springProfile>

    <!-- 预发环境日志目录 -->
    <springProfile name="pre">
        <property name="LOG_HOME" value="/export/log/${APP}"/>
    </springProfile>

    <!-- 生产环境日志目录 -->
    <springProfile name="prod">
        <property name="LOG_HOME" value="/export/log/${APP}"/>
    </springProfile>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-16t] %-5p %-22c{0} %X{ServiceId} - %X{traceId}%m%n</pattern>
        </encoder>
    </appender>
    <appender name="DETAIL" class="ch.qos.logback.core.rolling.RollingFileAppender" additivity="false">
        <File>${LOG_HOME}/${APP}_detail.log</File>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-16t] %-5p %-22c{0} %X{ServiceId} - %X{traceId}%m%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP}_detail.log.%d{yyyyMMdd}-%i</fileNamePattern>
            <!--日志保留时长-->
            <maxHistory>60</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
            <maxFileSize>200MB</maxFileSize>
        </rollingPolicy>
    </appender>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache.httpclient.wire" level="INFO"/>
    <logger name="org.apache.commons.httpclient" level="INFO"/>
    <logger name="org.apache.zookeeper" level="INFO"/>
    <logger name="com.wangyin.key.server.util" level="WARN"/>
    <logger name="com.jd.jim.cli" level="WARN"/>
    <logger name="com.wangyin.key" level="WARN"/>
    <logger name="com.wangyin.rediscluster.notification.service" level="WARN"/>
    <logger name="com.jd.jsf" level="WARN"/>
    <logger name="com.wangyin.schedule" level="WARN"/>
    <logger name="org.springframework.boot.web.servlet.support.ErrorPageFilter" level="OFF"/>
    <logger name="com.jd.laf.config" level="OFF"/>
    <logger name="reactor.netty" level="WARN"/>
<!--    <logger name="com.jdt.open" level="DEBUG"/>-->

    <root level="INFO">
        <appender-ref ref="DETAIL"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>