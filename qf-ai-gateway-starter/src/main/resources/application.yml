server:
  # 设定Httppost数据大小,这里改没用,因为是tomcat的应用类型,用的jdos的tomcat,需要在jdos配置管理里改server.xml
  max-http-header-size: 20MB
  port: 8011
  tomcat:
    max-swallow-size: 20MB
    max-http-form-post-size: 20MB
    max-threads: 600
    # 优化Tomcat连接器配置
    accept-count: 200
    max-connections: 8192
    connection-timeout: 5000
    keep-alive-timeout: 60000
    max-keep-alive-requests: 100
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,text/javascript,application/javascript

# OpenAI客户端优化配置
openai:
  api:
    max-connections: 500
    max-idle-time: 30000
#  context-path: /basic
pagehelper:
  helper-dialect: mysql
  reasonable: true

mybatis:
  configuration:
    map-underscore-to-camel-case: true

spring:
  profiles:
    active: "@profileActive@"
  application:
    name: qf-ai-server
  jackson:
    serialization: { WRITE_DATES_AS_TIMESTAMPS: true }
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true


# 发号器
identifier:
  cache:
    key:
      prefix: ${spring.application.name}

# open-lab
open:
  lab:
    business: qf-ai-gateway
    app: ${spring.application.name}
    api.packages: com.jd.qf.ai..*.api.*
    controller.packages: com.jd.qf.ai..*.facade.controller
  exception.handler:
    enable: false
    packages: ${open.lab.api.packages}
  sequence:
    enable: true
  log:
    enable: true
    packages: ${open.lab.api.packages},${open.lab.controller.packages}
  trace:
    enable: true
    packages: ${open.lab.api.packages},${open.lab.controller.packages}
  validator:
    enable: true
    packages: ${open.lab.api.packages},${open.lab.controller.packages}
  health:
    enable: true
  auto.cache:
    enable: true
    app: ${open.lab.app}
  event.flow:
    enable: false
    app: ${open.lab.app}
  jsf.log:
    max.length: 50000
  lock:
    enable: true
  identifier:
    enable: true
    key:
      prefix: ${spring.application.name}

#限流配置

  rate:
    limit:
      enable: true
      config: ducc
