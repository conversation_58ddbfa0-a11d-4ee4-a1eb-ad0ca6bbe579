# OpenAI配置示例
# 在实际的application.yml中添加以下配置

openaiConfig:
  # OpenAI API Key，也可以通过环境变量OPENAI_API_KEY设置
  apiKey: "your-openai-api-key-here"
  # OpenAI API Base URL，默认为https://api.openai.com/v1
  baseUrl: "https://api.openai.com/v1"
  # 或者使用Azure OpenAI的配置
  # baseUrl: "https://your-resource-name.openai.azure.com/openai/deployments/your-deployment-name"
  # apiKey: "your-azure-openai-api-key"

# 使用示例：
# 1. 在ChatReq中设置agentType为"OPENAI"
# 2. 设置model为OpenAI支持的模型，如"gpt-4", "gpt-3.5-turbo"等
# 3. 调用/qf/ai/chat/chat/syncChat或/qf/ai/chat/chat/streamChat接口
