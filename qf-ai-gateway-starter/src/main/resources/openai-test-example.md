# OpenAI聊天接口测试示例

## 配置说明

在使用前，请确保配置了OpenAI API Key：

### 方式1：环境变量
```bash
export OPENAI_API_KEY=your-openai-api-key-here
export OPENAI_BASE_URL=https://api.openai.com/v1  # 可选，默认值
```

### 方式2：配置文件
在 `application.yml` 中添加：
```yaml
openai:
  api:
    key: your-openai-api-key-here
    base-url: https://api.openai.com/v1  # 可选，默认值
```

## 接口测试

### 1. 非流式聊天完成

```bash
curl -X POST http://localhost:8011/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "stream": false,
    "max_tokens": 100,
    "temperature": 0.7
  }'
```

### 2. 流式聊天完成

```bash
curl -X POST http://localhost:8011/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Tell me a short story"
      }
    ],
    "stream": true,
    "max_tokens": 200,
    "temperature": 0.8
  }'
```

## 支持的模型

- gpt-4
- gpt-4-turbo
- gpt-3.5-turbo
- 其他OpenAI支持的模型

## 支持的参数

- `messages`: 消息列表（必需）
- `model`: 模型名称（必需）
- `stream`: 是否流式返回（可选，默认false）
- `max_tokens`: 最大token数（可选）
- `temperature`: 温度参数（可选，0-2）
- `top_p`: top_p参数（可选，0-1）
- `n`: 生成的选择数量（可选，默认1）
- `presence_penalty`: 存在惩罚（可选，-2到2）
- `frequency_penalty`: 频率惩罚（可选，-2到2）
- `user`: 用户标识（可选）

## 响应格式

### 非流式响应
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### 流式响应
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 错误处理

接口会返回标准的HTTP状态码和错误信息：

- 400: 请求参数错误
- 401: API Key无效
- 429: 请求频率限制
- 500: 服务器内部错误

错误响应格式：
```json
{
  "error": {
    "message": "错误描述",
    "type": "invalid_request_error",
    "code": "invalid_api_key"
  }
}
```
