aks:
  aliasName: qFi_05_001
  appId: qf-ai-server
  appName: qf-ai-server
  jsfAlias: AKS-JSF
  key:
    alias:
      name: ${aks.aliasName}

pro:
  jimdb:
    url: jim://3427078605513867862/29494

jdd:
  easyjob:
    appId: qf-ai-server
    enable: true
    host: http://schedule.jdfin.local
    secret: 28fc75f605979238fa40b96e138045c1

jsf:
  registry:
    index: i.jsf.jd.com
  provider:
    alias: prod
  alias:
    jr-custinfo-api-service-exp: pro

jmq:
  bizDomain:
    address: nameserver.jmq.jd.local:80
    app: qfaiserver
    user: qfaiserver
    password: 0c9125ad52284aacbf22f89df92b92cd
  topic:
    csChatBinlog: cs_chat_record_new_binlog
    delayProcessAiReply: delayProcessAiReply_prod
    forwardWsMessage: forward_ws_message_order_topicprod


ducc:
  appname: qf-ai-gateway
  configuration: prod
  domain: ducc.jd.local
  namespace: qf-ai-gateway
  profiles: DUCC_DEFAULT_PROFILE
  token: ce67e961c7904339842865b4469f46df
laf:
  config:
    logger:
      enabled: true
      key: logger.level
      type: logback
    manager:
      application: qf-ai-gateway
      parameters:
        - name: autoListener
          value: true
      resources:
        - name: qf-ai-gateway
          uri: ucc://${ducc.appname}:${ducc.token}@${ducc.domain}/v1/namespace/${ducc.namespace}/config/${ducc.configuration}/profiles/${ducc.profiles}?longPolling=15000&amp;necessary=true
open:
  act:
    log:
      app: bpomp-settle
      enable: true
  lab:
    env: prod
    app: bpomp-settle
  mybatis:
    encrypt:
      enable: true
oss:
  accessKey: 8ZUW3877NFFHIBOLG801HT3AAX0C1TLL
  bucket: mkt-crm
  endpoint: s3.jdpay.com
  export:
    temp:
      path: /export/temp
  secretKey: S8UW0IG5XSZIOM6DDY8JJNICA2OJO6EO
  signingRegion: tech-north-1
spring:
  datasource:
    url: ***************************************************************************************************************************************************************************************************************************************************
    username: mkt_scrm
    hikari:
      connection-init-sql: SET SESSION group_concat_max_len = 1000000
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB

dify:
  api:
    base-url: http://**************/v1
    timeout: 30000

chatRhino:
  api:
    base-url: http://api.chatrhino.jd.com/api/v2

llm:
  sec:
    url: http://llmsec-entry.jd.local/llmsec/api/defense/v2/
    accessKey: JDR_weidianyun_001
    secretKey: fd52bdd3cac84308afcafd829831e2ee

jdq:
  producerConfigs:
    ai_agent_input_output:
      user: P8cdeb189
      appDomain: ENTRY
      password: vW9qwz2XsCpha7Jb

jes:
  serverUrl: http://es-b8g7p5wl8q-http.es-b8g7p5wl8q.svc.guan-prod.gnhk.x.abchost.local:9200
  appName: wdy-cdp
  secret: 8bf858a8bddce1149c66c869e7316735
es:
  csChatIndex: prod_cs_chat_record_new

selfBuild:
  api:
    route:
      base-url: http://qfagent.jd.local
    chat:
      base-url: http://qfagent.jd.local

agent:
  api:
    base-url: http://localhost:8080/qf/ai/inner

scrm:
  robotGateway:
    api:
      base-url: http://wxwork-robot-gateway.jdfin.local