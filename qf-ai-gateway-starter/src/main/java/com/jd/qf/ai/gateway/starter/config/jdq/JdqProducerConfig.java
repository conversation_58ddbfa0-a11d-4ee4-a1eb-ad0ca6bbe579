package com.jd.qf.ai.gateway.starter.config.jdq;

import com.jd.bdp.jdq.JDQConfigUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/31
 */
@Configuration
@ConfigurationProperties(prefix = "jdq")
@Profile({"local","pre","prod"})
@Slf4j
@Data
public class JdqProducerConfig {

    private Map<String, JdqProducerProperties> producerConfigs;

    @Bean("jdqProducerFactory")
    public Map<String, KafkaProducer<String, String>> kafkaProducers() {

        Map<String, KafkaProducer<String, String>> producerMap = new HashMap<>();

        producerConfigs.forEach((topic, props) -> {
            Properties properties = new Properties();
            properties.putAll(JDQConfigUtil.getClientConfigs(
                    props.getUser(),
                    props.getAppDomain(),
                    props.getPassword()
            ));
            properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");//序列化方式是string
            properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");//序列化方式是string
            //lz4/gzip/snappy，建议lz4,gzip
            properties.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");
            //如果单条消息很大，可以尝试增加次参数进行调整  ，注意相应调整buffer.memory大小
            properties.put(ProducerConfig.BATCH_SIZE_CONFIG, "262144");
            ////sdk里面默认linger.ms是100,如果在单条同步发送条件下此值请用户设置成0(一般不建议)
            properties.put(ProducerConfig.LINGER_MS_CONFIG, "500");

            KafkaProducer<String, String> producer = new KafkaProducer<>(properties);
            producerMap.put(topic, producer);
            log.info("初始化Kafka Producer完成: {}", topic);
        });

        return producerMap;
    }
}
