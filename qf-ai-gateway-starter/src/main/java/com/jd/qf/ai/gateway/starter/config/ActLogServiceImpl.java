package com.jd.qf.ai.gateway.starter.config;

import com.jdt.open.capability.actlog.ActLogHttpHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 记录操作日志Bean
 * <AUTHOR>
 * @date 2024-12-25 18:46
 */
@Component
@Slf4j
public class ActLogServiceImpl extends ActLogHttpHandler {
    /**
     * 应用名称，用于记录操作日志的租户ID。
     */
    @Value("${open.lab.app}")
    String appName;
    @Override
    public String getTenantId() {
        return appName;
    }

    @Override
    public String getOperatorId() {
        return "";
    }
}
