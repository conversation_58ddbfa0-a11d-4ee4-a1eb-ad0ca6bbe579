package com.jd.qf.ai.gateway.function.util;

import com.alibaba.fastjson.JSONArray;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;

/**
 * 定义H2不支持的函数
 * @date 2025-01-20 16:05
 * <AUTHOR>
 */
public class H2Functions {

    /**
     * 根据指定格式将日期转换为字符串。
     * @param date 要转换的日期对象。
     * @param format 日期格式字符串，支持大多数SimpleDateFormat格式化符号，但将'm'替换为'M'。
     * @return 格式化后的日期字符串。
     */
    public static String dateFormat(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format.replace("%", "").replace("m", "M"));
        return sdf.format(date);
    }

    /**
     * 判断一个 JSON 数组是否包含另一个 JSON 数组。
     * @param jsonArray1 第一个 JSON 数组。
     * @param jsonArray2 第二个 JSON 数组。
     * @return 如果第一个 JSON 数组包含第二个 JSON 数组，则返回 true；否则返回 false。
     */
    public static boolean jsonContains(String jsonArray1, String jsonArray2) {
        if (jsonArray1 == null || jsonArray2 == null) {
            return false;
        }
        JSONArray array1 = JSONArray.parseArray(jsonArray1);
        JSONArray array2 = JSONArray.parseArray(jsonArray2);
        return new HashSet<>(array1).containsAll(array2);
    }

}