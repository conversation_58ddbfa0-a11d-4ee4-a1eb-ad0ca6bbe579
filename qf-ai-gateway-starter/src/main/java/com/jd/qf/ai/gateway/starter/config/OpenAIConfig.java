package com.jd.qf.ai.gateway.starter.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;

/**
 * OpenAI配置类
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@Configuration
public class OpenAIConfig {

    @Value("${openai.api.max-connections:500}")
    private int maxConnections;

    @Value("${openai.api.max-idle-time:30000}")
    private int maxIdleTime;

    /**
     * 配置OpenAI专用的WebClient
     */
    @Bean("openaiWebClient")
    public WebClient openaiWebClient() {
        // 配置连接池
        ConnectionProvider connectionProvider = ConnectionProvider.builder("openai-pool")
                .maxConnections(maxConnections)
                .maxIdleTime(Duration.ofMillis(maxIdleTime))
                .maxLifeTime(Duration.ofMinutes(10))
                .pendingAcquireTimeout(Duration.ofSeconds(5))
                .evictInBackground(Duration.ofSeconds(30))
                .build();

        return WebClient.builder()
                .defaultHeader("Content-Type", "application/json")
                .defaultHeader("Connection", "keep-alive")
                .clientConnector(new ReactorClientHttpConnector(
                        HttpClient.create(connectionProvider)
                ))
                .build();
    }
}
