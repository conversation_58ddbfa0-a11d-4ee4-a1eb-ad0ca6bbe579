package com.jd.qf.ai.gateway.starter.config;

import com.jdt.open.capability.config.OpenClientInfo;
import com.jdt.open.exception.InitializationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 核心配置服务
 * <AUTHOR>
 * @date 2024-12-25 18:46 2024-12-25 18:47
 */
@Configuration
@Slf4j
public class CoreConfig {
    /**
     * 业务线名称，用于标识日志所属的业务线。
     */
    private final String BUSINESS_NAME;

    /**
     * 应用程序的名称，用于标识日志所属的应用程序。
     */
    private final String APPLICATION_NAME;

    /**
     * 环境变量，用于确定当前运行环境。
     */
    private final String ENV;

    /**
     * 构造CoreConfig对象并初始化环境变量、业务线和应用名称。
     * @param openClientInfo OpenClientInfo对象，包含业务线和应用名称信息。
     * @param actLogEnv act.log.env环境变量，用于指定日志环境。
     * @param openLabEnv open.lab.env环境变量，用于指定open-lab环境。
     */
    public CoreConfig(OpenClientInfo openClientInfo,
                      @Value("${act.log.env:}")String actLogEnv,
                      @Value("${open.lab.env:}")String openLabEnv) {
        //open-lab环境
        if (StringUtils.isNotBlank(actLogEnv)) {
            ENV = actLogEnv;
        } else if (StringUtils.isNotBlank(openLabEnv)) {
            ENV = openLabEnv;
        } else {
            throw new InitializationException("[OpenActLog] 组件启动失败，请添加环境配置open.lab.env");
        }
        //业务线、应用名称
        if (StringUtils.isNotBlank(openClientInfo.getBusiness())) {
            BUSINESS_NAME = openClientInfo.getBusiness();
        } else {
            throw new InitializationException("[OpenActLog] 组件启动失败，请添加业务名称配置open.lab.business");
        }
        if (StringUtils.isNotBlank(openClientInfo.getApp())) {
            APPLICATION_NAME = openClientInfo.getApp();
        } else {
            throw new InitializationException("[OpenActLog] 组件启动失败，请添加应用名称配置open.lab.app");
        }
    }
}
