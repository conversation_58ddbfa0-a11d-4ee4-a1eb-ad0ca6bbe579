package com.jd.qf.ai.gateway.starter.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * oss配置工具类
 * @date 2024-12-25 18:47
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class OssConfig {
    /**
     * 当前环境
     */
    @Autowired
    private Environment environment;

    @Bean
    public AmazonS3 amazonS3Api(OssClientPro ossClientPro) {
        String env = environment.getActiveProfiles()[0];
        log.info("---OSS AmazonS3 内网客户端初始化开始，当前环境：{}", env);
        AmazonS3 amazonS3 = AmazonS3Client.builder()
                .withCredentials((new AWSStaticCredentialsProvider(new BasicAWSCredentials(ossClientPro.getAccessKey(), ossClientPro.getSecretKey()))))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(ossClientPro.getEndpoint(), ossClientPro.getSigningRegion()))
                //设置endpoint/bucket方式访问，非bucket.endpoint方式访问
                .withPathStyleAccessEnabled(true)
                //如果是公网endpoint，这里请填写HTTPS，如果是内网endpoint请填写HTTP
                .withClientConfiguration(new ClientConfiguration()
                        .withProtocol("prod".equals(env) || "pre".equals(env) ? Protocol.HTTPS : Protocol.HTTP)
                        .withSignerOverride("S3SignerType"))
                .build();
        log.info("---OSS AmazonS3 内网客户端初始化完成---");
        return amazonS3;
    }
}
