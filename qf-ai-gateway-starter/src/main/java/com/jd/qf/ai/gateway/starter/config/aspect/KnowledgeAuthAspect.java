package com.jd.qf.ai.gateway.starter.config.aspect;

import cn.hutool.core.util.StrUtil;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.knowledge.common.annotation.ValidateAuth;
import com.jdt.open.dto.GeneralResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 知识库鉴权切面
 * <AUTHOR>
 * @date 2025-01-02
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class KnowledgeAuthAspect {

    /**
     * 鉴权配置，key是appId，value是secretKey
     */
    @LafValue("knowledgeAuthConfig")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, String> authConfigMap = new HashMap<>();

    /**
     * 对带有@ValidateAuth注解的方法进行鉴权
     */
    @Around("@annotation(com.jd.qf.ai.knowledge.common.annotation.ValidateAuth) || " +
            "@within(com.jd.qf.ai.knowledge.common.annotation.ValidateAuth)")
    public Object validateAuth(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 获取注解信息
            ValidateAuth validateAuth = getValidateAuthAnnotation(joinPoint);
            if (validateAuth == null || !validateAuth.required()) {
                return joinPoint.proceed();
            }

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return GeneralResponse.error("无法获取请求信息");
            }

            HttpServletRequest request = attributes.getRequest();
            
            // 从请求头或参数中获取appId和secretKey
            String appId = getAppId(request);
            String secretKey = getSecretKey(request);

            if (StrUtil.isBlank(appId) || StrUtil.isBlank(secretKey)) {
                log.warn("知识库鉴权失败：appId或secretKey为空, appId={}, secretKey={}", appId, secretKey);
                return GeneralResponse.error("鉴权失败：appId或secretKey不能为空");
            }

            // 验证appId和secretKey
            if (!validateCredentials(appId, secretKey)) {
                log.warn("知识库鉴权失败：无效的appId或secretKey, appId={}", appId);
                return GeneralResponse.error("鉴权失败：无效的appId或secretKey");
            }

            log.debug("知识库鉴权成功: appId={}", appId);
            return joinPoint.proceed();

        } catch (Exception e) {
            log.error("知识库鉴权异常", e);
            return GeneralResponse.error("鉴权异常: " + e.getMessage());
        }
    }

    /**
     * 获取ValidateAuth注解
     */
    private ValidateAuth getValidateAuthAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 先检查方法上的注解
        ValidateAuth annotation = method.getAnnotation(ValidateAuth.class);
        if (annotation != null) {
            return annotation;
        }
        
        // 再检查类上的注解
        return method.getDeclaringClass().getAnnotation(ValidateAuth.class);
    }

    /**
     * 从请求中获取appId
     */
    private String getAppId(HttpServletRequest request) {
        // 优先从请求头获取
        String appId = request.getHeader("X-App-Id");
        if (StrUtil.isNotBlank(appId)) {
            return appId;
        }
        
        // 从参数获取
        appId = request.getParameter("appId");
        if (StrUtil.isNotBlank(appId)) {
            return appId;
        }
        
        return null;
    }

    /**
     * 从请求中获取secretKey
     */
    private String getSecretKey(HttpServletRequest request) {
        // 优先从请求头获取
        String secretKey = request.getHeader("X-Secret-Key");
        if (StrUtil.isNotBlank(secretKey)) {
            return secretKey;
        }
        
        // 从参数获取
        secretKey = request.getParameter("secretKey");
        if (StrUtil.isNotBlank(secretKey)) {
            return secretKey;
        }
        
        return null;
    }

    /**
     * 验证凭据
     */
    private boolean validateCredentials(String appId, String secretKey) {
        if (authConfigMap.isEmpty()) {
            log.warn("知识库鉴权配置为空，跳过鉴权验证");
            return true; // 如果没有配置，则跳过验证
        }
        
        String configSecretKey = authConfigMap.get(appId);
        return StrUtil.isNotBlank(configSecretKey) && configSecretKey.equals(secretKey);
    }
}
