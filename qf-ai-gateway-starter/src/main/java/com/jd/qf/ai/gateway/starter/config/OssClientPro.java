package com.jd.qf.ai.gateway.starter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * oss对象存储配置
 * @date 2024-12-25 18:47
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "oss")
@Component
public class OssClientPro {

    /**
     * OSS对象存储的访问密钥，用于身份验证和加密数据。
     */
    private String accessKey;

    /**
     * OSS对象存储的秘钥，用于身份验证和加密数据。
     */
    private String secretKey;

    /**
     * 签名区域，用于指定OSS服务的签名区域。
     */
    private String signingRegion;

    /**
     * OSS服务的访问端点。
     */
    private String endpoint;

    /**
     * 存储桶名称，用于标识存储资源的容器。
     */
    private String bucket;

}
