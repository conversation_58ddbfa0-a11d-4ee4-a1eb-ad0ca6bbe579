package com.jd.qf.ai.gateway.starter.config;

import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.ReloadableJimClientFactoryBean;
import com.jdt.open.capability.client.redis.JimOpenRedisClient;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * jimdb配置工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-25 18:47
 */
@Slf4j
@Configuration
public class JimDbConfig {

    @Bean
    public OpenRedisClient jimOpenRedisClient(Cluster jimClient) {
        return new JimOpenRedisClient(jimClient);
    }

    /**
     * 客户端配置
     */
    @Bean("jimClient")
    @ConditionalOnMissingBean
    public ReloadableJimClientFactoryBean cacheClusterClient(@Value("${pro.jimdb.url}") String jimDbUrl) throws Exception {
        ReloadableJimClientFactoryBean client = new ReloadableJimClientFactoryBean();
        client.setJimUrl(jimDbUrl);
        return client;
    }
}
