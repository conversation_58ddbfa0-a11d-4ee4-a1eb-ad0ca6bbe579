package com.jd.qf.ai.gateway.starter.config.aspect;

import com.jd.qf.ai.gateway.core.api.auth.AuthService;
import com.jd.qf.ai.gateway.core.api.auth.dto.AuthReq;
import com.openai.errors.UnauthorizedException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 校验apiKey切面
 * <AUTHOR>
 * @description
 * @date 2025/7/17
 */
@Aspect
@Component
public class ValidateApiKeyAspect {

    @Autowired
    AuthService authService;

    @Around("@annotation(com.jd.qf.ai.gateway.core.api.auth.anno.ValidateApiKey)")
    public Object checkBearerToken(ProceedingJoinPoint joinPoint) throws Throwable {

        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        String authHeader = request.getHeader("Authorization");

        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new IllegalArgumentException("apiKey不能为空");
        }

        String token = authHeader.substring(7);

        if (!authService.auth(AuthReq.builder().apiKey(token).build())) {
            throw new IllegalArgumentException("apiKey错误");
        }

        return joinPoint.proceed();
    }
}
