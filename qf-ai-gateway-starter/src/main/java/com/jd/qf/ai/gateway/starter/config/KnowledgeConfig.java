package com.jd.qf.ai.gateway.starter.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * 知识库配置
 * <AUTHOR>
 * @date 2025-01-02
 */
@Configuration
public class KnowledgeConfig {

    /**
     * 知识库MyBatis Plus拦截器
     * 暂时禁用分页插件，使用PageHelper统一处理分页
     */
    @Bean
    public MybatisPlusInterceptor knowledgeMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 暂时注释掉分页插件，避免与PageHelper冲突
        // interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 知识库自动填充处理器
     */
    @Bean
    public MetaObjectHandler knowledgeMetaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                LocalDateTime now = LocalDateTime.now();
                this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, now);
                this.strictInsertFill(metaObject, "modifiedTime", LocalDateTime.class, now);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "modifiedTime", LocalDateTime.class, LocalDateTime.now());
            }
        };
    }
}
