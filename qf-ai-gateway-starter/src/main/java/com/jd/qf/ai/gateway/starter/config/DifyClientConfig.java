package com.jd.qf.ai.gateway.starter.config;

import io.github.imfangs.dify.client.DifyClientFactory;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.model.DifyConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Dify客户端配置
 * <AUTHOR>
 * @description
 * @date 2025/5/23
 */
@Configuration
public class DifyClientConfig {

    @Bean
    public DifyDatasetsClient difyDatasetsClient(){
        // 使用自定义配置创建客户端
        DifyConfig config = DifyConfig.builder()
                .baseUrl("http://**************/v1")
                .apiKey("dataset-a3hTVeAFu8E164ipl30OHXqK")
                .connectTimeout(5000)
                .readTimeout(60000)
                .writeTimeout(30000)
                .build();
        return DifyClientFactory.createDatasetsClient(config);
    }
}
