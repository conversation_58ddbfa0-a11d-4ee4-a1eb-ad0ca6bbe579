package com.jd.qf.ai.gateway.starter;

import com.jd.jr.aks.security.SecurityPropertySourceFactory;
import com.jd.jr.aks.security.configuration.EnableAksSecurity;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 应用启动工具类
 * <AUTHOR>
 * @date 2024-12-25 18:46
 */
@SpringBootApplication(scanBasePackages = {"com.jd.qf.ai", "com.jdt"})
@ComponentScan(basePackages = "com.jd.qf.ai*")
@ImportResource(locations = {"classpath:META-INF/spring/spring.xml"})
@PropertySource(value = "classpath:important-${spring.profiles.active}.properties", factory = SecurityPropertySourceFactory.class)
@MapperScan({"com.jd.qf.ai.*.infrastructure.dao.mapper", "com.jd.qf.ai.knowledge.dao"})
@EnableAksSecurity
@EnableTransactionManagement
@Slf4j
public class Application extends SpringBootServletInitializer {
    /**
     * 自动注入的应用上下文，用于获取bean实例等操作。
     */
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 启动Spring Boot应用程序
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(Application.class);
    }
}
