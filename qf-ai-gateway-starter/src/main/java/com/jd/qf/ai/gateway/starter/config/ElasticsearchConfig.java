package com.jd.qf.ai.gateway.starter.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Elasticsearch配置
 * <AUTHOR>
 * @date 2025-01-02
 */
@Configuration
public class ElasticsearchConfig {

    @Value("${jes.serverUrl:http://es-b8g7p5wl8q-http.es-b8g7p5wl8q.svc.guan-prod.gnhk.x.abchost.local:9200}")
    private String serverUrl;

    @Value("${jes.appName:wdy-cdp}")
    private String appName;

    @Value("${jes.secret:8bf858a8bddce1149c66c869e7316735}")
    private String secret;

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        // 解析URL
        String[] urlParts = serverUrl.replace("http://", "").replace("https://", "").split(":");
        String hostname = urlParts[0];
        int port = urlParts.length > 1 ? Integer.parseInt(urlParts[1]) : 9200;

        // 创建HttpHost
        HttpHost httpHost = new HttpHost(hostname, port, "http");

        // 创建RestClientBuilder
        RestClientBuilder builder = RestClient.builder(httpHost);

        // 如果有认证信息，设置认证
        if (appName != null && secret != null && !appName.isEmpty() && !secret.isEmpty()) {
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, 
                new UsernamePasswordCredentials(appName, secret));
            
            builder.setHttpClientConfigCallback(httpClientBuilder -> 
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
        }

        // 设置超时时间
        builder.setRequestConfigCallback(requestConfigBuilder -> 
            requestConfigBuilder
                .setConnectTimeout(5000)
                .setSocketTimeout(60000)
                .setConnectionRequestTimeout(1000));

        return new RestHighLevelClient(builder);
    }
}
