package com.jd.qf.ai.gateway.starter.config.aspect;

import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.gateway.common.pojo.auth.SignReq;
import com.jd.qf.ai.gateway.common.pojo.constants.RespCodeConstants;
import com.jd.qf.ai.gateway.common.pojo.request.BaseRequest;
import com.jd.qf.ai.gateway.common.pojo.resp.BizResponse;
import com.jd.qf.ai.gateway.common.pojo.result.ResponseEnum;
import com.jd.qf.ai.gateway.common.pojo.utils.AuthUtils;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import com.jdt.open.capability.ratelimiter.RateLimiterException;
import com.jdt.open.dto.GeneralResponse;
import com.jdt.open.exception.BizException;
import com.jdt.open.exception.RpcException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.joda.time.DateTime;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 控制器切面
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
@Slf4j
@Aspect
@Component
@Order(1)
public class ControllerAspect {

    @LafValue("appAuthMap")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, String> appAuthMap = new HashMap<>();

    @Autowired
    private OpenRedisClient openRedisClient;
    private static final String REQUEST_TIME_LIMIT_CACHE_KEY = "REQUEST_TIME_LIMIT_CACHE_KEY_";


    /**
     * 对所有开放控制器方法进行处理
     */
    @Around("execution(* com.jd.qf.ai.gateway.facade.controller.out.*.*(..))")
    public Object handleOpenControllerMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            doAuth(joinPoint);
            return joinPoint.proceed();
        } catch (Throwable e) {
            //处理全局异常
            return handleException(e, joinPoint);
        }
    }

    /**
     * 对所有内部控制器方法进行处理
     */
    @Around("execution(* com.jd.qf.ai.gateway.facade.controller..*.*(..))")
    public Object handleInnerControllerMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            return joinPoint.proceed();
        } catch (Throwable e) {
            //处理全局异常
            return handleException(e, joinPoint);
        }
    }



    /**
     * 验证签名
     * @param joinPoint 切点
     */
    private void doAuth(ProceedingJoinPoint joinPoint) {
        //验签
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Class<?> parameterType = method.getParameterTypes()[0];
        if (BaseRequest.class.isAssignableFrom(parameterType)) {

            BaseRequest<?> request = (BaseRequest<?>) joinPoint.getArgs()[0];
            //验证时间戳防止重放
            Long timestamp = checkTimeStamp(signature, request);

            //进行验签
            String secretKey = appAuthMap.get(request.getAppKey());
            if (secretKey == null || !secretKey.equals(request.getSecretKey())) {
                throw new IllegalArgumentException("appKey或secretKey错误");
            }
            String paramSign = request.getSign();
            SignReq<Object> signReq = SignReq.builder()
                    .appKey(request.getAppKey())
                    .secretKey(request.getSecretKey())
                    .timestamp(timestamp)
                    .params(request.getParams())
                    .build();
            String sign = AuthUtils.sign(signReq);
            if (!paramSign.equals(sign)) {
                throw new IllegalArgumentException("验签失败");
            }
        }
    }

    private Long checkTimeStamp(MethodSignature signature, BaseRequest<?> request) {

        String fullMethodName = signature.getDeclaringTypeName() + "." + signature.getName();
        Long timestamp = request.getTimestamp();
        Date requestDate = new Date(timestamp);
        Date currentDate3Before = new DateTime(new Date()).minusMinutes(3).toDate();
        Date currentDate3After = new DateTime(new Date()).plusMinutes(3).toDate();
        if (requestDate.before(currentDate3Before) || requestDate.after(currentDate3After)) {
            log.warn("请求时间已过期");
            throw new IllegalArgumentException("请求时间已过期");
        }

        //针对同一个方法,同一个appKey,同一个时间戳,同一个签名MD5,只允许请求一次
        String cacheKey = REQUEST_TIME_LIMIT_CACHE_KEY + fullMethodName + request.getAppKey() + timestamp + request.getSign();
        String timestampCache = openRedisClient.get(cacheKey);
        if (StringUtils.isBlank(timestampCache)) {
            openRedisClient.setEx(cacheKey, String.valueOf(timestamp), 6, TimeUnit.MINUTES);
        } else {
            log.warn("请求过于频繁，请稍后再试");
            throw new IllegalArgumentException("请求过于频繁，请稍后再试");
        }
        return timestamp;
    }

    /**
     * 处理异常
     * @param e 异常
     * @param joinPoint 切点
     * @return 处理结果
     */
    private Object handleException(Throwable e, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Class<?> returnType = method.getReturnType();

        // 根据异常类型和返回类型处理
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            log.warn("业务异常: [{}] {}", bizException.getCode(), bizException.getMessage());
            return wrapResponse(returnType, bizException.getCode(), bizException.getMessage());
        } else if (e instanceof ConstraintViolationException) {
            ConstraintViolationException cve = (ConstraintViolationException) e;
            Set<ConstraintViolation<?>> violations = cve.getConstraintViolations();
            String message = violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", "));
            log.warn("参数校验异常: {}", message);
            return wrapResponse(returnType, ResponseEnum.PARAM_ERROR.getCode(), message);
        } else if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException manve = (MethodArgumentNotValidException) e;
            return wrapResponse(returnType, ResponseEnum.PARAM_ERROR.getCode(), getBindingResultMessage(manve.getBindingResult()));
        } else if (e instanceof BindException) {
            BindException be = (BindException) e;
            return wrapResponse(returnType, ResponseEnum.PARAM_ERROR.getCode(), getBindingResultMessage(be.getBindingResult()));
        } else if (e instanceof IllegalArgumentException) {
            log.warn("参数异常: {}", e.getMessage());
            return wrapResponse(returnType, ResponseEnum.PARAM_ERROR.getCode(), e.getMessage());
        } else if (e instanceof RpcException) {
            log.error("RPC调用异常", e);
            return wrapResponse(returnType, ResponseEnum.FAILURE.getCode(), "服务调用异常: " + e.getMessage());
        } else if (e instanceof FileSizeLimitExceededException) {
            log.warn("文件大小超限: {}", e.getMessage());
            return wrapResponse(returnType, ResponseEnum.PARAM_ERROR.getCode(), e.getMessage());
        } else if (e instanceof RateLimiterException) {
            log.warn("限流异常: {}", e.getMessage());
            return wrapResponse(returnType, ResponseEnum.FAILURE.getCode(), "请求过于频繁，请稍后再试");
        } else if (e instanceof WebClientResponseException) {
            WebClientResponseException wcre = (WebClientResponseException) e;
            log.error("WebClient调用异常: {} {}", wcre.getStatusCode(), wcre.getStatusText(), e);
            return wrapResponse(returnType, ResponseEnum.FAILURE.getCode(), "远程服务调用异常: " + wcre.getStatusText());
        } else {
            log.error("系统异常", e);
            return wrapResponse(returnType, ResponseEnum.FAILURE.getCode(), ResponseEnum.FAILURE.getMsg());
        }
    }

    /**
     * 获取绑定结果的错误信息
     * @param bindingResult 绑定结果
     * @return 错误信息
     */
    private String getBindingResultMessage(BindingResult bindingResult) {
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        String message = fieldErrors.stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        log.warn("参数校验异常: {}", message);
        return message;
    }

    /**
     * 根据返回类型包装响应
     * @param returnType 返回类型
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @return 包装后的响应
     */
    private Object wrapResponse(Class<?> returnType, String errorCode, String errorMessage) {
        GeneralResponse<?> errorResponse = GeneralResponse.error(errorCode, errorMessage);

        if (Publisher.class.isAssignableFrom(returnType)) {
            return Mono.just(errorResponse);
        }  else if (returnType.isAssignableFrom(BizResponse.class)) {
            return BizResponse.error(RespCodeConstants.ERROR, errorMessage);
        }
        else {
            return errorResponse;
        }
    }
}
