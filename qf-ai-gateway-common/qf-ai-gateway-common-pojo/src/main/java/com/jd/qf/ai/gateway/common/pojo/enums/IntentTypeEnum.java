package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 意图识别结果枚举
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IntentTypeEnum {

    /**
     * 回复
     */
    REPLY("REPLY", "回复"),

    /**
     * 不回复
     */
    NO_REPLY("NO_REPLY", "不回复"),

    /**
     * SOP结束
     */
    END("SOP_END", "SOP结束")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (IntentTypeEnum intentTypeEnum : IntentTypeEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (IntentTypeEnum intentTypeEnum : IntentTypeEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
