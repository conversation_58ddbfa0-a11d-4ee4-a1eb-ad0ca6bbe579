package com.jd.qf.ai.gateway.common.pojo.utils;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jd.qf.ai.gateway.common.pojo.auth.SignReq;
import lombok.extern.slf4j.Slf4j;

/**
 * 鉴权工具类
 * <AUTHOR>
 * @description
 * @date 2025/4/17
 */
@Slf4j
public class AuthUtils {

    /**
     * 签名
     * @param request 入参
     * @return 验证结果
     */
    public static String sign(SignReq<?> request){

        //序列化时需要对象的字段进行排序
        String jsonString = JSON.toJSONString(request, SerializerFeature.SortField);
        MD5 md5 = MD5.create();
        return md5.digestHex(jsonString);
    }
}
