package com.jd.qf.ai.gateway.common.pojo.result;

import com.jdt.open.exception.ICodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用响应枚举
 * <AUTHOR>
 * @date 2024-12-25 18:41 2024-12-25 18:42
 */

@AllArgsConstructor
@Getter
public enum ResponseEnum implements ICodeEnum<String> {
    /**
     * 成功
     */
    SUCCESS("00000", "成功"),
    /**
     * 单据已存在
     */
    EXIST("00001", "单据已存在"),
    /**
     * 参数错误
     */
    PARAM_ERROR("20000", "参数错误"),
    /**
     * 待实名
     */
    NOT_REAL("22000", "待实名"),
    /**
     * 待入驻
     */
    NOT_APPLY("22001", "待入驻"),
    /**
     * 非管理员无法入驻
     */
    NOT_ADMIN("22002", "非管理员无法入驻"),
    /**
     * 单据不存在
     */
    NOT_EXIST("40000", "单据不存在"),
    /**
     * 失败
     */
    FAILURE("50000", "系统繁忙,请稍后再试!"),

    /**
     * 重复的客户名称
     */
    REPEAT_NAME("60001", "已存在同一客户公司名称"),

    /**
     * 重复的收入提报明细
     */
    REPEAT_REVENUE_DETAIL("60002", "重复的收入提报明细"),

    /**
     * 发票校验异常
     */
    CHECK_INVOICE_ERROR("70001", "发票校验异常"),

    /**
     * 需要二次确认
     */
    CHECK_NEED_CONFIRM("80001", "需要二次确认"),
    ;
    /**
     * 状态码
     */
    private final String code;
    /**
     * 消息
     */
    private final String msg;
}
