package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Agent类型枚举
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AgentTypeEnum {

    /**
     * dify
     */
    DIFY("DIFY", "difyChatHandler"),

    /**
     * 言犀
     */
    CHAT_RHINO("CHAT_RHINO", "chatRhinoHandler"),

    /**
     * 自建
     */
    SELF_BUILD("SELF_BUILD", "selfBuildChatHandler"),

    /**
     * OpenAI
     */
    OPENAI("OPENAI", "openAIChatHandler")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * 处理器beanName
     */
    private final String beanName;

    public static String getBeanNameByCode(String code){

        for (AgentTypeEnum agentTypeEnum : AgentTypeEnum.values()) {

            if(agentTypeEnum.getCode().equals(code)){
                return agentTypeEnum.getBeanName();
            }
        }
        return null;
    }

}
