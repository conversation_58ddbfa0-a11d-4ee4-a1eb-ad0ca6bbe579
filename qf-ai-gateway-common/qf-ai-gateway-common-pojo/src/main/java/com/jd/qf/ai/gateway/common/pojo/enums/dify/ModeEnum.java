package com.jd.qf.ai.gateway.common.pojo.enums.dify;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 索引类型
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModeEnum {

    /**
     * 自动
     */
    AUTOMATIC("automatic", "自动"),

    /**
     * 自定义
     */
    CUSTOM("custom", "自定义")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (ModeEnum intentTypeEnum : ModeEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (ModeEnum intentTypeEnum : ModeEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
