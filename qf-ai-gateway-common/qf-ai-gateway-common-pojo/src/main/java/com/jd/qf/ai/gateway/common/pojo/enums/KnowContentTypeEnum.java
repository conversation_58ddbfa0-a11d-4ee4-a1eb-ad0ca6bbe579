package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库类型枚举
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowContentTypeEnum {

    /**
     * 问题库
     */
    QUESTION("QUESTION", "_问题库"),

    /**
     * 问题和答案库
     */
    QA("QA", "_问题和答案库")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (KnowContentTypeEnum intentTypeEnum : KnowContentTypeEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (KnowContentTypeEnum intentTypeEnum : KnowContentTypeEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
