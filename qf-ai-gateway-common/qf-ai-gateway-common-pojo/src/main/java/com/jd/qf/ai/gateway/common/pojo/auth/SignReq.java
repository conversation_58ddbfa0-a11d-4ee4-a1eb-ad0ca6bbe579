package com.jd.qf.ai.gateway.common.pojo.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 签名请求
 * <AUTHOR>
 * @description
 * @date 2025/4/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SignReq<T> {

    /**
     * appKey,应用标识
     */
    private String appKey;

    /**
     * secretKey,应用秘钥
     */
    private String secretKey;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求参数泛型
     */
    private T params;
}
