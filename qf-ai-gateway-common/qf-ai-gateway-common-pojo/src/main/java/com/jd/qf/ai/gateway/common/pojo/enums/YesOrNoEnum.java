package com.jd.qf.ai.gateway.common.pojo.enums;

import com.jd.qf.ai.gateway.common.pojo.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否标记枚举
 * @date 2024-12-25 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum YesOrNoEnum implements BaseEnum<Integer> {
    /**
     * 否
     */
    NO(0, "否"),
    /**
     * 正常
     */
    YES(1, "是"),
    ;
    /**
     * 枚举的唯一标识符
     */
    private final Integer code;
    /**
     * 枚举的描述信息
     */
    private final String msg;


}
