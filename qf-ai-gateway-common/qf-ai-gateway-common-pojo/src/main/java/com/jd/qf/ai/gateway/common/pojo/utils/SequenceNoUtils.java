package com.jd.qf.ai.gateway.common.pojo.utils;

import cn.hutool.core.lang.Snowflake;

/**
 * 序列号工具类
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
public class SequenceNoUtils {

    private SequenceNoUtils() {}

    private static class SnowflakeHolder {
        private static final Snowflake INSTANCE = new Snowflake();
    }

    public static String getSequenceNo() {
        return SnowflakeHolder.INSTANCE.nextIdStr();
    }
}
