package com.jd.qf.ai.gateway.common.pojo.enums.dify;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 索引类型
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DifyPermissionEnum {

    /**
     * 仅自己
     */
    ONLY_ME("only_me", "仅自己"),

    /**
     * 所有团队成员
     */
    ALL("all_team_members", "所有团队成员"),

    /**
     * 部分团队成员
     */
    PARTIAL("partial_members", "部分团队成员")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (DifyPermissionEnum intentTypeEnum : DifyPermissionEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (DifyPermissionEnum intentTypeEnum : DifyPermissionEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
