package com.jd.qf.ai.gateway.common.pojo.enums.dify;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 在 Q&A 模式下，指定文档的语言
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DocLanguageEnum {

    /**
     * 中文
     */
    CHINESE("Chinese", "中文"),

    /**
     *  英文
     */
    ENGLISH("English", "英文")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (DocLanguageEnum intentTypeEnum : DocLanguageEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (DocLanguageEnum intentTypeEnum : DocLanguageEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
