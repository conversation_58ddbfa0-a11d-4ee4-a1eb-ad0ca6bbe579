package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.Getter;

/**
 * 是或否通用枚举
 * <AUTHOR>
 * @date 2024/3/21 10:16
 */
public enum YesOrNoStringEnum {

    /**
     * 是
     */
    YES("Y", "是"),
    /**
     * 否
     */
    NO("N", "否"),
    ;

    /**
     * 枚举的代码值。
     */
    @Getter
    private final String code;
    /**
     * 枚举的描述信息。
     */
    @Getter
    private final String desc;

    /**
     * 构造一个 YesOrNoStringEnum 对象。
     * @param code 枚举的代码值。
     * @param desc 枚举的描述信息。
     */
    YesOrNoStringEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
