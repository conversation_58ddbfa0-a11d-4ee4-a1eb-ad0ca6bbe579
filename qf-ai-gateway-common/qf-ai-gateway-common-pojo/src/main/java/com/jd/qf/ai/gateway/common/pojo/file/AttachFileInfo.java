package com.jd.qf.ai.gateway.common.pojo.file;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 通用附件信息
 * @date 2024-12-25 18:41
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AttachFileInfo {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 文件编号
     */
    private String fileNo;

    /**
     * 文件的base64转成的md5
     */
    private String base64Md5;


    /**
    * 附件Key
    */
    @NotBlank(message = "附件Key不能为空")
    @Size(max = 512, message = "附件Key不能超过512")
    private String fileKey;

    /**
    * 附件名称
    */
    @NotBlank(message = "附件名称不能为空")
    @Size(max = 200, message = "附件名称不能超过200")
    private String fileName;

    /**
    * 附件url
    */
    private String fileUrl;

}