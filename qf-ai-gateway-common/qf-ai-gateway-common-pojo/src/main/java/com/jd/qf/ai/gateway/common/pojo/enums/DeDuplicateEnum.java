package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 去重策略枚举
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Getter
@AllArgsConstructor
public enum DeDuplicateEnum {

    /**
     * 跳过重复的问题
     */
    CONTINUE("CONTINUE", "跳过"),

    /**
     * 覆盖重复的问题
     */
    COVER("COVER", "覆盖")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static DeDuplicateEnum getByCode(String code) {
        for (DeDuplicateEnum deduplicateEnum : DeDuplicateEnum.values()) {
            if (deduplicateEnum.getCode().equals(code)) {
                return deduplicateEnum;
            }
        }
        return null;
    }

    public static String getCodeByMsg(String msg) {
        for (DeDuplicateEnum deduplicateEnum : DeDuplicateEnum.values()) {
            if (deduplicateEnum.getMsg().equals(msg)) {
                return deduplicateEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (DeDuplicateEnum deduplicateEnum : DeDuplicateEnum.values()) {
            if (deduplicateEnum.getCode().equals(code)) {
                return deduplicateEnum.getMsg();
            }
        }
        return null;
    }
}
