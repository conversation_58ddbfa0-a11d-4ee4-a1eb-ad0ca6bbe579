package com.jd.qf.ai.gateway.common.pojo.enums.dify;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 索引类型
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DocFormEnum {

    /**
     * 直接 embedding
     */
    TEXT_MODEL("text_model", "直接 embedding"),

    /**
     *  parent-child 模式
     */
    PARENT_CHILD("hierarchical_model", " parent-child 模式"),

    /**
     * Q&A 模式：为分片文档生成 Q&A 对，然后对问题进行 embedding
     */
    QA_MODEL("qa_model", "Q&A")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (DocFormEnum intentTypeEnum : DocFormEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (DocFormEnum intentTypeEnum : DocFormEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
