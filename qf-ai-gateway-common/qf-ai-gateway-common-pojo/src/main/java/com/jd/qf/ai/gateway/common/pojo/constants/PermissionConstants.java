package com.jd.qf.ai.gateway.common.pojo.constants;

import java.util.Collections;
import java.util.List;

/**
 * 权限常量工具
 * <AUTHOR>
 * @date 2024/3/29 10:06
 */
public class PermissionConstants {
    /**
     * 不正确的权限值
     * 用于没有数据权限或者报错的时候,通过筛选错误的权限字段值,给用户返回空值
     */
    public static final List<String> ERROR_PERMISSION = Collections.singletonList("ERROR_PERMISSION");

    /**
     * 业务线字段
     */
    public static final String BUSINESS_LINE = "business_line";

    /**
     * 业务类型编码字段
     */
    public static final String BUSINESS_TYPE_CODE = "business_code";

    /**
     * 是否有科技权限
     */
    public static final String HAS_PMS = "1";

}
