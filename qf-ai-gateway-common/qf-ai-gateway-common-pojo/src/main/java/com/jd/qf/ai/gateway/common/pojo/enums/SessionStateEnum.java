package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会话状态枚举
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Getter
@AllArgsConstructor
public enum SessionStateEnum {

    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中"),

    /**
     * SOP结束
     */
    SOP_END("SOP_END", "SOP结束")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static SessionStateEnum getByCode(String code) {
        for (SessionStateEnum deduplicateEnum : SessionStateEnum.values()) {
            if (deduplicateEnum.getCode().equals(code)) {
                return deduplicateEnum;
            }
        }
        return null;
    }

    public static String getCodeByMsg(String msg) {
        for (SessionStateEnum deduplicateEnum : SessionStateEnum.values()) {
            if (deduplicateEnum.getMsg().equals(msg)) {
                return deduplicateEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (SessionStateEnum deduplicateEnum : SessionStateEnum.values()) {
            if (deduplicateEnum.getCode().equals(code)) {
                return deduplicateEnum.getMsg();
            }
        }
        return null;
    }
}
