package com.jd.qf.ai.gateway.common.pojo.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 基础请求对象
 * <AUTHOR>
 * @description
 * @date 2025/4/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseRequest<T> {

    /**
     * appKey,应用标识
     */
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    /**
     * secretKey,应用秘钥
     */
    @NotBlank(message = "secretKey不能为空")
    private String secretKey;

    /**
     * 时间戳
     */
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;

    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;

    /**
     * 请求参数泛型
     */
    @Valid
    private T params;
}
