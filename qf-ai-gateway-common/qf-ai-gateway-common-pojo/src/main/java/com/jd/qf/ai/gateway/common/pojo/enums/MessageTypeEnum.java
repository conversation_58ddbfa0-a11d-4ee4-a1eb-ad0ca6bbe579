package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Agent类型枚举
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {

    /**
     * 文本消息
     */
    MESSAGE("message", "文本消息"),

    /**
     * 消息流结束
     */
    MESSAGE_END("message_end", "消息流结束")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    public static String getDescByCode(String code){

        for (MessageTypeEnum agentTypeEnum : MessageTypeEnum.values()) {

            if(agentTypeEnum.getCode().equals(code)){
                return agentTypeEnum.getDesc();
            }
        }
        return null;
    }

}
