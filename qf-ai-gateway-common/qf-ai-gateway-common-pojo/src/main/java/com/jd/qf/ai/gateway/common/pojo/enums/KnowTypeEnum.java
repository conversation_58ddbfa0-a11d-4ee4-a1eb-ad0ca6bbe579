package com.jd.qf.ai.gateway.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 知识库类型枚举
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum KnowTypeEnum {

    /**
     * 回复
     */
    DIFY("DIFY", "DIFY")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (KnowTypeEnum intentTypeEnum : KnowTypeEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (KnowTypeEnum intentTypeEnum : KnowTypeEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
