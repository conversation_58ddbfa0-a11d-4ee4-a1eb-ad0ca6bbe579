package com.jd.qf.ai.gateway.common.pojo.enums.dify;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求方式枚举
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RequestTypeEnum {

    /**
     * 前端
     */
    FRONTEND("FRONTEND", "前端"),

    /**
     * 后端MQ
     */
    BACKEND_MQ("BACKEND_MQ", "后端MQ")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (RequestTypeEnum intentTypeEnum : RequestTypeEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (RequestTypeEnum intentTypeEnum : RequestTypeEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
