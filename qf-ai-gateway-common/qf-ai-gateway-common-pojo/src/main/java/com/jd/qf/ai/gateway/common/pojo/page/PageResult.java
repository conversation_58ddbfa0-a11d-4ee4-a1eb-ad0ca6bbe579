package com.jd.qf.ai.gateway.common.pojo.page;


import lombok.Data;
import lombok.NonNull;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用分页结果响应
 * <AUTHOR>
 * @date 2024-12-25 18:41 2024-12-25 18:41
 */
@Data
public class PageResult<T> {

    /**
     * 总记录数
     */
    private long total;
    /**
     * 存储分页结果的数据列表
     */
    private List<T> list = new ArrayList<>();

    /**
     * 将数据列表和总数转换为分页结果对象。
     * @param dataList 数据列表
     * @param total 总记录数
     * @return 分页结果对象
     */
    public static <T> PageResult<T> of(List<T> dataList, long total) {
        PageResult<T> pageResult = new PageResult<T>() {
        };
        pageResult.setList(dataList);
        pageResult.setTotal(total);
        return pageResult;
    }
    /**
     * 将PageResult<T>中的数据列表通过给定的Function映射为新的数据类型R，并返回新的PageResult<R>。
     * @param dataList 需要映射的PageResult对象
     * @param function 用于将T类型元素转换为R类型元素的函数
     * @return 映射后的PageResult<R>对象，若dataList为空则返回null
     */
    public static <T,R> PageResult<R> map(PageResult<T> dataList, @NonNull Function<T,R> function) {
        return Optional.ofNullable(dataList)
                .map(pageResult -> {
                    List<R> collect = Collections.emptyList();
                    if (pageResult.getList() != null) {
                        collect = pageResult.getList().stream().map(function).collect(Collectors.toList());
                    }
                    return PageResult.of(collect,pageResult.getTotal());
                }).orElse(null);
    }

}
