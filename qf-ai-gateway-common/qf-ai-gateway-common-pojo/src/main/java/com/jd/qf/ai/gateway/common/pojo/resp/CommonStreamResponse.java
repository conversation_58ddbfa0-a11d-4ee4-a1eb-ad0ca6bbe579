package com.jd.qf.ai.gateway.common.pojo.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流式聊天返回值
 * <AUTHOR>
 * @description
 * @date 2025/3/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CommonStreamResponse {

    /**
     * 事件类型
     */
    private String event;

    /**
     * 任务的唯一标识符
     */
    private String taskId;

    /**
     * 响应消息的唯一标识符
     */
    private String id;

    /**
     * 表示对话的唯一标识符
     */
    private String conversationId;

    /**
     * 指定聊天的模式或类型
     */
    private String mode;

    /**
     * 存储聊天回复的具体内容
     */
    private String answer;

    /**
     * 表示聊天回复创建的时间戳
     */
    private String createdAt;
}
