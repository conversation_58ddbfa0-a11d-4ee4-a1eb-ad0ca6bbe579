package com.jd.qf.ai.gateway.common.pojo.file;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 需要转储的文件信息对象
 * <AUTHOR>
 * @date 2024-12-25 18:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransStoreFileInfo {

    /**
    * 附件名称
    */
    @NotBlank(message = "附件名称不能为空")
    @Size(min = 1, max = 100, message = "附件名称长度必须在1到100个字符之间")
    private String fileName;

    /**
    * 附件url
    */
    @NotBlank(message = "附件url不能为空")
    private String fileUrl;

    /**
     * 附件类型(后缀,不带.)
     */
    @NotBlank(message = "附件类型不能为空")
    private String fileType;

}