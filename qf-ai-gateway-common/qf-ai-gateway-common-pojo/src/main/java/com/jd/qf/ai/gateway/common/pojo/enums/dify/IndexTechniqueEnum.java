package com.jd.qf.ai.gateway.common.pojo.enums.dify;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 索引类型
 * @date 2025-04-02 18:40
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IndexTechniqueEnum {

    /**
     * 高质量
     */
    HIGH_QUALITY("high_quality", "高质量"),

    /**
     * 经济
     */
    ECONOMY("economy", "经济")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static String getCodeByMsg(String msg) {
        for (IndexTechniqueEnum intentTypeEnum : IndexTechniqueEnum.values()) {
            if (intentTypeEnum.getMsg().equals(msg)) {
                return intentTypeEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (IndexTechniqueEnum intentTypeEnum : IndexTechniqueEnum.values()) {
            if (intentTypeEnum.getCode().equals(code)) {
                return intentTypeEnum.getMsg();
            }
        }
        return null;
    }


}
