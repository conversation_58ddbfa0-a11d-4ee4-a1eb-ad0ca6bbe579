package com.jd.qf.ai.gateway.common.lang.util;

import com.jd.qf.ai.gateway.common.pojo.BaseEnum;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集合工具通用类
 *
 * <AUTHOR>
 * @date 2024-12-25 18:36
 */
@SuppressWarnings("unused")
public class CollectionUtil {
    /**
     * 判断集合是否为null 或 空
     * @param collection 目标集合
     * @return 是否
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
    /**
     * 判断集合是否为null 或 空
     * @param collection 目标集合
     * @return 是否
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * 判断数组是否为null 或 空
     * @param arrays 目标集合
     * @return 是否
     */
    public static boolean isEmpty(Object[] arrays) {
        return arrays == null || arrays.length == 0;
    }

    /**
     * 生成Set集合
     * @param obs 参数列表
     * @param <T> 参数泛型
     * @return set
     */
    @SafeVarargs
    public static <T> Set<T> asSet(T... obs) {
        return new HashSet<>(Arrays.asList(obs));
    }

    /**
     * 集合处理转换<br/>
     * @param from 目标集合
     * @param func 对集合中元素的处理
     * @param <T> 集合泛型
     * @param <U> 转换泛型
     * @return 转换后的list
     */
    public static <T, U> List<U> convertList(List<T> from, Function<T, U> func) {
        if(from == null){
            return null;
        }
        return from.stream().map(func).collect(Collectors.toList());
    }
    /**
     * 枚举集合转换成code集合
     */
    public static <E,T extends BaseEnum<E>> List<E> convertEnumsToList(List<T> from) {
        if(isEmpty(from)){
            return null;
        }
        return from.stream().filter(Objects::nonNull).map(BaseEnum::getCode).collect(Collectors.toList());
    }
    /**
     * 枚举code集合转换成枚举
     */
    public static <T,E extends BaseEnum<T>> List<E> convertListToEnums(List<T> from,Class<E> clazz) {
        if(isEmpty(from)){
            return null;
        }
        return from.stream().filter(Objects::nonNull).map(e->EnumUtil.getEnumByCode(clazz,e)).collect(Collectors.toList());
    }
    /**
     * 集合处理转换成set<br/>
     * @param from 目标集合
     * @param func 对集合中元素的处理
     * @param <T> 集合泛型
     * @param <U> 转换泛型
     * @return 转换后的set
     */
    public static <T, U> Set<U> convertSet(List<T> from, Function<T, U> func) {
        return from.stream().map(func).collect(Collectors.toSet());
    }
    /**
     * 集合处理转换成map(没有value)<br/>
     * @param from 目标集合
     * @param keyFunc 对集合中元素的处理
     * @param <T> 集合泛型
     * @param <K> 转换map key的泛型
     * @return 转换后的map
     */
    public static <T, K> Map<K, T> convertMap(List<T> from, Function<T, K> keyFunc) {
        if(from == null){
            return null;
        }
        return from.stream().collect(Collectors.toMap(keyFunc, item -> item));
    }

    /**
     * 集合转map,并对value操作<br/>
     * @param from 目标集合
     * @param keyFunc 转换key的函数
     * @param valueFunc 转换value的函数
     * @param <T> 集合泛型
     * @param <K> key泛型
     * @param <V> value泛型
     * @return 转换后的map
     */
    public static <T, K, V> Map<K, V> convertMap(List<T> from, Function<T, K> keyFunc, Function<T, V> valueFunc) {
        return from.stream().collect(Collectors.toMap(keyFunc, valueFunc));
    }

    /**
     * 按指定规则分组集合成map
     * @param from 目标集合
     * @param keyFunc 分组规则
     * @param <T> 集合泛型
     * @param <K> map key 泛型
     * @return 分组后的map
     */
    public static <T, K> Map<K, List<T>> convertMultiMap(List<T> from, Function<T, K> keyFunc) {
        return from.stream().collect(Collectors.groupingBy(keyFunc,
                Collectors.mapping(t -> t, Collectors.toList())));
    }

    /**
     * (分组value是list)按指定规则对集合元素进行处理,然后再通过指定规则对集合进行分组成map(先对集合元素进行处理再分组)
     * @param from 目标集合
     * @param keyFunc 分组规则,并指定分组key
     * @param valueFunc 集合元素处理规则
     * @param <T> 集合泛型
     * @param <K> 分组key泛型
     * @param <V> 分组value泛型
     * @return 分组后的map
     */
    public static <T, K, V> Map<K, List<V>> convertMultiMap(List<T> from, Function<T, K> keyFunc, Function<T, V> valueFunc) {
        return from.stream().collect(Collectors.groupingBy(keyFunc,
                Collectors.mapping(valueFunc, Collectors.toList())));
    }

    /**
     * (分组value是set)按指定规则对集合元素进行处理,然后再通过指定规则对集合进行分组成map(先对集合元素进行处理再分组)
     * @param from 目标集合
     * @param keyFunc 分组规则,并指定分组key
     * @param valueFunc 集合元素处理规则
     * @param <T> 集合泛型
     * @param <K> 分组key泛型
     * @param <V> 分组value泛型
     * @return 分组后的map
     */
    public static <T, K, V> Map<K, Set<V>> convertMultiMap2(List<T> from, Function<T, K> keyFunc, Function<T, V> valueFunc) {
        return from.stream().collect(Collectors.groupingBy(keyFunc, Collectors.mapping(valueFunc, Collectors.toSet())));
    }

    /**
     * 判断子集
     * @param source 源集合
     * @param candidates 判断集合
     * @return 如果candidates是source的子集,返回true
     */
    public static boolean containsAny(Collection<?> source, Collection<?> candidates) {
        return org.springframework.util.CollectionUtils.containsAny(source, candidates);
    }

    /**
     * 获得集合的第一个元素
     * @param from 目标集合
     * @param <T> 集合泛型
     * @return 元素
     */
    public static <T> T getFirst(List<T> from) {
        return !isEmpty(from) ? from.get(0) : null;
    }

    /**
     * map遍历value成string
     */
    public static String mapValuesToString(Map<?,?> map){
        StringBuilder str = new StringBuilder();
        for (Object k : map.keySet()) {
            Object v = map.get(k);
            str.append(v).append(";");
        }
        return str.toString();
    }

    @SafeVarargs
    public static <T> boolean isInclude(T data,T... collect){
        for (T t : collect) {
            if (t.equals(data)){
                return true;
            }
        }
        return false;
    }

    /**
     * map的value转换
     * @param map 原map
     * @param function 转换函数
     * @param <T> KEY类型
     * @param <V> 被转换的类型
     * @param <R> 要转换的类型
     */
    public static <T,V,R> Map<T,R> mapConvertValue(Map<T,V> map,Function<V,R> function){
        return Optional.ofNullable(map)
            .map(m->{
                Map<T, R> mp = new HashMap<>(map.size());
                map.forEach((k,v)-> mp.put(k,function.apply(v)));
                return mp;
            }).orElse(Collections.emptyMap());

    }
    /**
     * 判断集合中是否存在重复项
     */
    public static <T> boolean existRepetition(Iterable<T> iterable,Comparator<T> comparator){
        TreeSet<T> treeSet = new TreeSet<>(comparator);
        if (iterable==null){
            return false;
        }
        for (T item : iterable) {
            if (!treeSet.add(item)){
                return true;
            }
        }
        return false;
    }

    /**
     * 字符串集合拼接
     */
    public static String join(Collection<String> list, String delimiter) {
        if (list == null) {
            return null;
        }
        if (delimiter == null) {
            delimiter = "";
        }
        StringJoiner joiner = new StringJoiner(delimiter);
        list.forEach(joiner::add);
        return joiner.toString();
    }
    /**
     * 自定义数组去重
     */
    public static <T> List<T> distinct(List<T> array,Comparator<T> comparator){
        if (array==null){
            return null;
        }
        TreeSet<T> treeSet = new TreeSet<>(comparator);
        treeSet.addAll(array);
        return new ArrayList<>(treeSet);
    }
}
