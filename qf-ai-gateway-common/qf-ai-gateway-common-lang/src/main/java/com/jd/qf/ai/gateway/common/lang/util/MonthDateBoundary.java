package com.jd.qf.ai.gateway.common.lang.util;

import lombok.Data;

import java.util.Date;

/**
 * 两个日期之间的边界
 * <AUTHOR>
 * @date 2025-02-14 10:08
 */
@Data
public class MonthDateBoundary {
        /**
         * 表示月份的起始日期。
         */
        public Date firstDay;
        /**
         * 表示月份的结束日期。
         */
        public Date lastDay;

        /**
         * 构造一个指定日期范围的月份边界对象。
         * @param firstDay 起始日期
         * @param lastDay 结束日期
         */
        public MonthDateBoundary(Date firstDay, Date lastDay) {
            this.firstDay = firstDay;
            this.lastDay = lastDay;
        }

    }