package com.jd.qf.ai.gateway.common.lang.pageutil;

import com.github.pagehelper.PageHelper;
import com.jd.qf.ai.gateway.common.pojo.page.PageResult;
import com.jd.qf.ai.gateway.common.pojo.page.PageQuery;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * 数据库分页工具
 *
 * <AUTHOR>
 * @date 2024-12-25 18:34
 */
public class DBPageUtils {

    /**
     * 分页查询,入参必须是 {@link PageQuery}
     *
     * @param query    查询入参
     * @param consumer 执行mapper
     * @param <T>      返回的泛型
     * @param <Q>      查询的泛型
     * @return {@link PageResult}
     */
    public static <T, Q extends PageQuery> PageResult<T> pageQuery(Q query, Consumer<Q> consumer) {
        return Optional.ofNullable(query)
                .filter(q -> consumer != null)
                .map(q -> PageHelper.startPage(q)
                        .<T>doSelectPageSerializable(() -> consumer.accept(q)))
                .map(data -> PageResult.of(data.getList(), data.getTotal()))
                .orElse(new PageResult<>() {
                });
    }
}
