package com.jd.qf.ai.gateway.common.lang.util;


import java.net.MalformedURLException;
import java.net.URL;

/**
 * 京东SSRF检查工具类
 * <AUTHOR>
 * @date 2023-07-20 16:44
 */
public class JdSsrfCheckUtil {
    /**
     * 校验url是否正确
     */
    public static boolean jdSsrfCheck(String urlString) throws MalformedURLException {
        URL urlObj = new URL(urlString);
        //定义请求协议白名单列表
        String[] allowProtocols = new String[]{"http", "https"};
        boolean protocolCheck = false;

        // 首先进行协议校验，若协议校验不通过，SSRF校验不通过
        String protocol = urlObj.getProtocol();
        for (String item : allowProtocols) {
            if (protocol.equals(item)) {
                protocolCheck = true;
                break;
            }
        }
        if (protocolCheck) {
            return true;
        } else {
            return false;
        }
    }
}

