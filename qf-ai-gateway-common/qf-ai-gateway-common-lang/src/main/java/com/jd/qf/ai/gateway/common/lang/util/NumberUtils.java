package com.jd.qf.ai.gateway.common.lang.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 数字工具服务类
 * <AUTHOR>
 * @date 2024/10/8 11:28
 */
@Slf4j
public class NumberUtils {

    /**
     * 验证非空
     */
    public static void validateNotNull(Object value, String message) throws IllegalArgumentException {
        if (value == null) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 验证大于0
     */
    public static void validatePositiveOrZero(BigDecimal value, String message) throws IllegalArgumentException {
        if (value.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 验证位数
     */
    public static void validateDigits(BigDecimal value, int maxInteger, int maxFraction, String message) throws IllegalArgumentException {
        String[] parts = value.stripTrailingZeros().toPlainString().split("\\.");
        int integerPartLength = parts[0].replace("-", "").length();
        int fractionPartLength = parts.length > 1 ? parts[1].length() : 0;

        if (integerPartLength > maxInteger || fractionPartLength > maxFraction) {
            throw new IllegalArgumentException(message);
        }
    }


    /**
     * 将字符串转换为BigDecimal类型，若字符串为空或转换失败则返回0。
     * @param str 待转换的字符串
     * @return 转换后的BigDecimal对象
     */
    public static BigDecimal toBigDecimalFromString(String str) {
        if (StrUtil.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(str);
        } catch (Exception e) {
            log.warn("字符串【{}】转BigDecimal异常【{}】，返回0", str, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

}
