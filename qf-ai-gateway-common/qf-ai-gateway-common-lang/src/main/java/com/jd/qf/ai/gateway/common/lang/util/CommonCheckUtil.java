package com.jd.qf.ai.gateway.common.lang.util;

import cn.hutool.core.util.StrUtil;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

/**
 * 通用校验工具类
 * @date 2024-12-07 15:09
 * <AUTHOR>
 */
@Slf4j
public class CommonCheckUtil {
    /**
     * 检查对象是否为null，如果是则抛出异常。
     * @param obj 需要检查的对象。
     * @param errMsg 如果obj为null时抛出的异常的错误信息。
     */
    public static void checkNotNull(Object obj, String errMsg) {
        if (obj == null) {
            log.warn("对象是null, {}", errMsg);
            throw new BizException(errMsg);
        }
    }

    /**
     * 检查对象是否为null，若为null则抛出异常。
     * @param obj 要检查的对象
     * @param errMsg 异常信息
     * @param code 异常代码
     */
    public static void checkNotNull(Object obj, String errMsg, String code) {
        if (obj == null) {
            log.warn("对象是null, {}, code={}", errMsg, code);
            throw new BizException(code, errMsg);
        }
    }

    /**
     * 检查字符串是否为空或空白，如果是则抛出业务异常。
     * @param str 需要检查的字符串对象。
     * @param errMsg 异常信息。
     */
    public static void checkNotBlank(String str, String errMsg) {
        if (StrUtil.isBlank(str)) {
            log.warn("字符串isBlank, {}", errMsg);
            throw new BizException(errMsg);
        }
    }

    /**
     * 检查两个对象是否相等，若不相等则抛出异常。
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param errMsg 异常信息
     */
    public static void checkEquals(Object obj1, Object obj2, String errMsg) {
        if (obj1 == null || !obj1.equals(obj2)) {
            log.warn("对象不相等【{}】【{}】{}", obj1, obj2, errMsg);
            throw new BizException(errMsg);
        }
    }

    /**
     * 检查两个对象是否相等，若不相等则抛出业务异常。
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param errMsg 异常信息
     * @param code 异常代码
     */
    public static void checkEquals(Object obj1, Object obj2, String errMsg, String code) {
        if (obj1 == null || !obj1.equals(obj2)) {
            log.warn("对象不相等【{}】【{}】{}, code={}", obj1, obj2, errMsg, code);
            throw new BizException(code, errMsg);
        }
    }

    /**
     * 检查是否存在指定的对象或记录，如果存在则抛出业务异常。
     * @param existCount 存在的对象或记录数量
     * @param errMsg 异常信息
     */
    public static void checkHasExist(int existCount, String errMsg) {
        if (existCount > 0) {
            log.warn("existCount【{}】>0, {}", existCount, errMsg);
            throw new BizException(errMsg);
        }
    }

    /**
     * 检查是否存在指定的条件，并在存在时抛出业务异常。
     * @param existCount 存在的数量。
     * @param errMsg 异常信息。
     * @param code 异常代码。
     * @throws BizException 如果 existCount 大于 0，会抛出该异常。
     */
    public static void checkHasExist(int existCount, String errMsg, String code) {
        if (existCount > 0) {
            log.warn("existCount【{}】>0, {}, code={}", existCount, errMsg, code);
            throw new BizException(code, errMsg);
        }
    }

    /**
     * 从Map中获取指定key对应的Date类型值。
     * @param map 包含要查找的Date类型值的Map对象。
     * @param key 在Map中查找的键。
     * @return 指定key对应的Date类型值。
     * @throws BizException 如果Map中不存在指定key或对应的值不是Date类型。
     */
    public static Date getDateFromMap(Map<String, Object> map, String key) {
        Object val = map.get(key);
        if (val instanceof Date date) {
            return date;
        }
        log.warn("getDateFromMap【{}】key【{}】val【{}】is not Date", map, key, val);
        throw new BizException("从Map中获取Date类型属性失败");
    }

    /**
     * 检查条件是否为真，否则抛出业务异常。
     * @param isTrue 条件是否为真
     * @param errMsg 错误消息
     */
    public static void checkIsTrue(boolean isTrue, String errMsg) {
        if (!isTrue) {
            log.warn("{}", errMsg);
            throw new BizException(errMsg);
        }
    }

    /**
     * 检查指定条件是否为真，否则抛出业务异常。
     * @param isTrue 需要检查的条件
     * @param errMsg 异常消息
     * @param code 异常代码
     */
    public static void checkIsTrue(boolean isTrue, String errMsg, String code) {
        if (!isTrue) {
            log.warn("{}, code={}", errMsg, code);
            throw new BizException(code, errMsg);
        }
    }
}
