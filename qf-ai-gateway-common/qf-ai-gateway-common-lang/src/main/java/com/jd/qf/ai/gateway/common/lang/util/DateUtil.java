package com.jd.qf.ai.gateway.common.lang.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 通用日期工具类
 *
 * <AUTHOR>
 * @date 2024-12-16 17:06
 */
@Slf4j
@SuppressWarnings("unused")
public class DateUtil {

    /**
     * 默认日期字符串格式 "yyyy-MM-dd"
     */
    public final static String DATE_DEFAULT = "yyyy-MM-dd";
    /**
     * 日期字符串格式 "yyyyMM"
     */
    public final static String DATE_YYYY = "yyyy";
    /**
     * 日期字符串格式 "yyyyMM"
     */
    public final static String DATE_YYYYMM = "yyyyMM";

    /**
     * 日期字符串格式 "yyyyMMdd"
     */
    public final static String DATE_YYYYMMDD = "yyyyMMdd";

    /**
     * 日期字符串格式 "yyyy-MM"
     */
    public final static String DATE_YYYY_MM = "yyyy-MM";

    /**
     * 日期字符串格式 "yyyy-MM-dd"
     */
    public final static String DATE_YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 默认日时字符串格式 "yyyy-MM-dd HH:mm:ss"
     */
    public final static String DATETIME_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日时字符串格式 "yyyy-MM-dd HH:mm"
     */
    public final static String DATETIME_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    /**
     * 日时字符串格式 "yyyy-MM-dd HH:mm:ss"
     */
    public final static String DATETIME_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日时字符串格式 "yyyy-MM-dd HH:mm:ss.SSS"
     */
    public final static String DATETIME_YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 默认时间字符串格式 "HH:mm:ss"
     */
    public final static String TIME_DEFAULT = "HH:mm:ss";

    /**
     * 默认时间字符串格式 "HH:mm"
     */
    public final static String TIME_HH_MM = "HH:mm";

    /**
     * 默认时间字符串格式 "HH:mm:ss"
     */
    public final static String TIME_HH_MM_SS = "HH:mm:ss";

    /**
     * 默认日时字符串格式，用于格式化日期时间为字符串。
     */
    public final static String DATE_TIME_DEFAULT = "yyyy-MM-dd HH:mm:ss";
    /**
     * 日期字符串格式 'yyyyMMddHHmmss'
     */
    public final static String DATE = "yyyyMMddHHmmss";
    /**
     * 日期字符串格式 'yyyy年MM月dd日'
     */
    public final static String DATE_CHINESE = "yyyy年MM月dd日";
    /**
     * 日期字符串格式 'yyyy年MM月'，用于格式化月份为中文字符串。
     */
    public final static String DATE_CHINESE_AS_MONTH = "yyyy年MM月";


    /**
     * 年
     */
    public static final int YEAR = 0;
    /**
     * 月
     */
    public static final int MONTH = 1;
    /**
     * 周
     */
    public static final int WEEK = 2;
    /**
     * 天
     */
    public static final int DAY = 3;
    /**
     * 小时
     */
    public static final int HOUR = 4;
    /**
     * 分
     */
    public static final int MINUTE = 5;
    /**
     * 秒
     */
    public static final int SECOND = 6;
    /**
     * 毫秒
     */
    public static final int MILLISECOND = 7;

    /**
     * 秒-毫秒数
     */
    private static final long MILLIS_PER_SECOND = 1000;
    /**
     * 分钟-毫秒数
     */
    private static final long MILLIS_PER_MINUTE = 60 * MILLIS_PER_SECOND;
    /**
     * 小时-毫秒数
     */
    private static final long MILLIS_PER_HOUR = 60 * MILLIS_PER_MINUTE;
    /**
     * 天-毫秒数
     */
    private static final long MILLIS_PER_DAY = 24 * MILLIS_PER_HOUR;

    /**
     * 获取系统当前时间
     *
     * @return 时间
     */
    public static Date now() {
        return new Date(System.currentTimeMillis());
    }

    /**
     * 判断某个日期是星期几
     *
     * @param date 时间
     * @return 周几
     */
    public static int getWeek(Date date) {
        if (date == null) {
            throw new RuntimeException("日期不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int week = c.get(Calendar.DAY_OF_WEEK) - 1;
        return week == 0 ? 7 : week;
    }

    /**
     * 获取给定时间的年
     *
     * @param date 时间
     * @return 那年
     */
    public static int getYear(Date date) {
        if (date == null) {
            throw new RuntimeException("日期不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.YEAR);
    }

    /**
     * 获取给定时间的月份
     *
     * @param date 时间
     * @return 哪月
     */
    public static int getMonth(Date date) {
        if (date == null) {
            throw new RuntimeException("日期不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取给定时间的日
     *
     * @param date 时间
     * @return 那天
     */
    public static int getDay(Date date) {
        if (date == null) {
            throw new RuntimeException("日期不能为空");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DATE);
    }

    /**
     * 获取指定日期零点日期
     *
     * @return 指定日期0点时间
     */
    public static Date getDayZeroTime(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获取指定日期所在月的第一天日期
     *
     * @return 指定月第一天
     */
    public static Date getFirstDayOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DATE, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获取指定日期所在月的第一天日期
     *
     * @return 指定月第一天日期
     */
    public static Date getStringFormatFirstDay(String dateStr, String format) {
        if (dateStr == null || format == null) {
            return null;
        }
        Date date = stringFormatToDate(dateStr, format);
        return getFirstDayOfMonth(date);
    }

    /**
     * 获取指定日期所在月的最后一天日期
     *
     * @return 指定月最后一天
     */
    public static Date getLastDayOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE));
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    /**
     * 获取指定日期所在月的最后一天日期
     *
     * @return 指定月最后一天
     */
    public static Date getStringFormatLastDay(String dateStr, String format) {
        if (dateStr == null || format == null) {
            return null;
        }
        Date date = stringFormatToDate(dateStr, format);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE));
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTime();
    }

    /**
     * Date转字符串
     *
     * @param date   时间
     * @param format 格式
     * @return 时间字符串
     */
    public static String dateFormatToString(Date date, String format) {
        if (date == null) {
            return null;
        }
        return dateFormatToString(dateToLocalDateTime(date), format);
    }

    /**
     * localDateTime转字符串
     *
     * @param date   localDateTime
     * @param format 格式
     * @return 时间字符串
     */
    public static String dateFormatToString(LocalDateTime date, String format) {
        return DateTimeFormatter.ofPattern(format).format(date);
    }

    /**
     * localDate转字符串
     *
     * @param date   localDate
     * @param format 格式
     * @return 时间字符串
     */
    public static String dateFormatToString(LocalDate date, String format) {
        return DateTimeFormatter.ofPattern(format).format(date);
    }

    /**
     * String转Date
     *
     * @param dateStr 时间字符串
     * @param format  格式
     * @return 时间
     */
    public static Date stringFormatToDate(String dateStr, String format) {
        if (dateStr == null || dateStr.isEmpty() || format == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat;
        Date date;
        try {
            simpleDateFormat = new SimpleDateFormat(format);
            date = simpleDateFormat.parse(dateStr);
            return date;
        } catch (Exception e) {
            log.error("字符串序列化时间失败", e);
            throw new RuntimeException("字符串序列化时间失败");
        }
    }

    /**
     * Date转LocalDateTime
     *
     * @param date 时间
     * @return localDateTime时间
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * Date转LocalDate
     *
     * @param date 时间
     * @return localDate时间
     */
    public static LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * LocalDate转Date
     *
     * @param localDate 时间
     * @return 时间
     */
    public static Date localDateToDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDateTime转Date
     *
     * @param localDateTime 时间
     * @return 时间
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 在给定时间基础上加减指定的月份，返回加减之后的日期
     */
    public static Date addMonth(Date date, int value) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, value);
        return c.getTime();
    }

    /**
     * 在给定时间基础上加减指定的月份，返回加减之后的日期
     */
    public static Date stringFormatAddMonth(String dateStr, String format, int value) {
        if (dateStr == null || format == null) {
            return null;
        }
        Date date = stringFormatToDate(dateStr, format);
        return addMonth(date, value);
    }

    /**
     * 在给定时间基础上加减指定的日期，返回加减之后的日期
     */
    public static Date addDay(Date date, int value) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, value);
        return c.getTime();
    }

    /**
     * 在给定时间基础上加减指定的日期，返回加减之后的日期
     */
    public static Date stringFormatAddDay(String dateStr, String format, int value) {
        if (dateStr == null || format == null) {
            return null;
        }
        Date date = stringFormatToDate(dateStr, format);
        return addDay(date, value);
    }

    /**
     * 转换yyyy-mm为年-月
     */
    public static String convertToYearMonth(String dateStr) {
        if (dateStr == null) {
            return null;
        }
        String[] dateArr = dateStr.split("-");
        return dateArr[0] + "年" + dateArr[1] + "月";
    }

    /**
     * 生成时间
     *
     * @param year       年
     * @param month      月
     * @param dayOfMonth 日
     * @return date
     */
    public static Date of(int year, int month, int dayOfMonth) {
        return localDateToDate(LocalDate.of(year, month, dayOfMonth));
    }

    /**
     * 获取当前时间下一天零点的时间
     */
    public static LocalDateTime getNextZero() {
        return LocalDateTime.now()
                .plusDays(1)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
    }

    /**
     * 获取当前时间到下一天零点的秒数
     *
     * @return 秒
     */
    public static int getToNextZeroSecond() {
        return (int) ChronoUnit.SECONDS.between(LocalDateTime.now(), getNextZero());
    }

    /**
     * 判断时间字符串是否符合格式
     */
    public static boolean isValidity(String dateStr, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        try {
            simpleDateFormat.parse(dateStr);
        } catch (Exception e) {
            log.error("不符合的时间格式");
            return false;
        }
        return true;
    }

    /**
     * localDateTime转字符串
     *
     * @param date localDateTime
     * @return 时间字符串
     */
    public static String defaultDateFormatToString(LocalDateTime date) {
        if (date == null) {
            return null;
        }
        return DateTimeFormatter.ofPattern(DATETIME_DEFAULT).format(date);
    }

    /**
     * 将 Date 对象转换为 LocalDateTime 对象。
     * @param date 需要转换的 Date 对象。
     * @return 转换后的 LocalDateTime 对象，若 date 为 null 则返回 null。
     */
    public static LocalDateTime fromDateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
    }

    /**
     * 将LocalDateTime对象转换为Date对象。
     * @param localDateTime 要转换的LocalDateTime对象。
     * @return 转换后的Date对象；如果localDateTime为null，则返回null。
     */
    public static Date fromLocalDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * 获取月第一天
     */
    public static LocalDate getFirstDate(LocalDate now) {
        if (now == null) {
            return null;
        }
        return now.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }

    /**
     * 获取月第一天
     */
    public static LocalDateTime getFirstDateOnMonth(LocalDateTime now) {
        if (now == null) {
            return null;
        }
        return now.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }

    /**
     * 获取年第一天
     */
    public static LocalDateTime getFirstDateOnYear(LocalDateTime now) {
        if (now == null) {
            return null;
        }
        return now.with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIN);
    }

    /**
     * 获取本月最后一天
     */
    public static LocalDate getLastDate(LocalDate now) {
        if (now == null) {
            return null;
        }
        return now.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 时间是否在同一个年月
     */
    public static boolean equalsYearAndMonth(Date... dates) {
        return dateYearMonthConditionalFilter(YearMonthFunction.isEquals(), dates);
    }

    /**
     * 判断时间是否按年月排列
     */
    public static boolean isYearMonthSort(Date... dates) {
        return dateYearMonthConditionalFilter(YearMonthFunction.isPast(), dates);
    }

    /**
     * 年月函数判断
     */
    public static boolean dateYearMonthConditionalFilter(YearMonthFunction func, Date... dates) {
        Integer year = null;
        Integer month = null;
        if (func == null || CollectionUtil.isEmpty(dates)) {
            return false;
        }
        boolean first = true;
        for (Date date : dates) {
            if (date == null) {
                return false;
            }
            Integer y = getYear(date);
            Integer m = getMonth(date);
            if (first) {
                year = y;
                month = m;
                first = false;
                continue;
            }
            if (!func.condition(year, month, y, m)) {
                return false;
            }
            year = y;
            month = m;
        }
        return true;
    }


    @FunctionalInterface
    public interface YearMonthFunction {
        /**
         * 新旧时间年月判断条件
         *
         * @param previousYear  旧的年
         * @param previousMonth 旧的月
         * @param year          新的年
         * @param month         新的月
         */
        boolean condition(Integer previousYear, Integer previousMonth, Integer year, Integer month);

        /**
         * 新的时间年月晚于旧的时间年月
         */
        static YearMonthFunction isPast() {
            return (previousYear, previousMonth, year, month) ->
                    previousYear < year || (previousYear.equals(year) && previousMonth <= month);
        }

        /**
         * 新旧的年月是否一致
         */
        static YearMonthFunction isEquals() {
            return (previousYear, previousMonth, year, month) ->
                    previousYear.equals(year) && previousMonth.equals(month);
        }
    }


    /**
     * 获取当前时间->次日00:00:00 ms值
     */
    public static Long getNowToNextDaySeconds() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis());
    }

    @Getter
    @AllArgsConstructor
    public enum IntervalEnum {
        /**
         * 左开右开
         */
        LEFT_OPEN_RIGHT_OPEN("()"),
        /**
         * 左开右闭
         */
        LEFT_OPEN_RIGHT_CLOSE("(]"),
        /**
         * 左闭右开
         */
        LEFT_CLOSE_RIGHT_OPEN("[)"),
        /**
         * 左闭右闭
         */
        LEFT_CLOSE_RIGHT_CLOSE("[]"),
        ;
        /**
         * 区间
         */
        public final String interval;
    }

    /**
     * 获取指定日期范围内的年月列表。
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 年月列表，每个元素格式为"yyyy-MM"
     */
    public static List<String> getYearMonthList(Date startDate, Date endDate) {
        List<String> yearMonthList = new ArrayList<>();

        // 将 Date 转换为 LocalDate
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 创建 YearMonth 对象
        YearMonth startYearMonth = YearMonth.from(start);
        YearMonth endYearMonth = YearMonth.from(end);

        // 循环添加年月到列表
        while (!startYearMonth.isAfter(endYearMonth)) {
            yearMonthList.add(startYearMonth.toString());
            startYearMonth = startYearMonth.plusMonths(1);
        }

        return yearMonthList;
    }

    /**
     * 获取指定日期范围内每个月的第一天。
     * @param startDate 起始日期，包含在内。
     * @param endDate 结束日期，不包含在内。
     * @return 每个月的第一天的日期列表。
     */
    public static List<Date> getFirstDaysOfMonths(Date startDate, Date endDate) {
        List<Date> result = new ArrayList<>();

        // 将 Date 转换为 LocalDate
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 获取开始月份的第一天
        YearMonth yearMonth = YearMonth.from(start);

        // 循环直到到达或超过结束日期
        while (!yearMonth.atDay(1).isAfter(end)) {
            LocalDate firstDayOfMonth = yearMonth.atDay(1);
            result.add(Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            yearMonth = yearMonth.plusMonths(1);
        }

        return result;
    }


    /**
     * 获取指定日期范围内每个月的起始和结束日期。
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 包含每个月起始和结束日期的列表
     */
    public static List<MonthDateBoundary> getMonthBoundaries(Date startDate, Date endDate) {
        List<MonthDateBoundary> result = new ArrayList<>();

        // 将 Date 转换为 LocalDate
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 获取开始月份
        YearMonth yearMonth = YearMonth.from(start);

        // 循环直到到达或超过结束日期
        while (!yearMonth.atDay(1).isAfter(end)) {
            LocalDate firstDayOfMonth = yearMonth.atDay(1);
            LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

            Date firstDay = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date lastDay = Date.from(lastDayOfMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());

            result.add(new MonthDateBoundary(firstDay, lastDay));
            yearMonth = yearMonth.plusMonths(1);
        }

        return result;
    }

    /**
     * 计算指定日期范围内每个月的天数。
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 每个月的天数，键为月份的第一天，值为该月的天数
     */
    public static Map<Date, Integer> calculateDaysPerMonth(Date startDate, Date endDate) {
        Map<Date, Integer> result = new TreeMap<>();

        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        LocalDate firstOfMonth = start.withDayOfMonth(1);

        while (!firstOfMonth.isAfter(end)) {
            YearMonth yearMonth = YearMonth.from(firstOfMonth);
            LocalDate lastOfMonth = yearMonth.atEndOfMonth();

            int daysInMonth;
            if (yearMonth.equals(YearMonth.from(end))) {
                // 如果是最后一个月，计算到结束日期
                daysInMonth = end.getDayOfMonth();
            } else {
                // 否则，计算整个月的天数
                daysInMonth = yearMonth.lengthOfMonth();
            }

            // 如果是第一个月，调整天数
            if (yearMonth.equals(YearMonth.from(start))) {
                daysInMonth -= start.getDayOfMonth() - 1;
            }

            Date key = Date.from(firstOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
            result.put(key, daysInMonth);

            // 移动到下一个月
            firstOfMonth = firstOfMonth.plusMonths(1);
        }

        return result;
    }

}
