package com.jd.qf.ai.gateway.common.lang.util;

import com.jd.qf.ai.gateway.common.pojo.BaseEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 枚举通用工具类
 * @date 2024-12-25 18:36
 * <AUTHOR>
 */
public class EnumUtil {

    /**
     * 返回指定编码的'枚举'
     */
    public static <K, T extends BaseEnum<K>> T getEnumByCode(Class<T> clazz, K code) {
        for (T aEnum : clazz.getEnumConstants()) {
            if (aEnum.getCode().equals(code)) {
                return aEnum;
            }
        }
        return null;
    }

    /**
     * 返回指定名称的'枚举'
     *
     */
    public static <T extends BaseEnum<?>> T getEnumByMsg(Class<T> clazz, String msg) {
        for (T aEnum : clazz.getEnumConstants()) {
            if (aEnum.getMsg().equals(msg)) {
                return aEnum;
            }
        }

        return null;
    }



    /**
     * 获取枚举的code
     */
    public static <K> K getEnumCode(BaseEnum<K> baseEnum) {
        return Optional.ofNullable(baseEnum)
                .map(BaseEnum::getCode)
                .orElse(null);
    }

    /**
     * 获取枚举的msg
     */
    public static String getEnumMsg(BaseEnum<?> baseEnum) {
        return Optional.ofNullable(baseEnum)
                .map(BaseEnum::getMsg)
                .orElse(null);
    }

    /**
     * 返回指定编码的'枚举'msg
     */
    public static <K, T extends BaseEnum<K>> String getEnumMsgByCode(Class<T> clazz, K code) {
        T t = getEnumByCode(clazz, code);
        if (t != null) {
            return t.getMsg();
        }
        return null;
    }

    /**
     * 获取枚举的所有code
     */
    public static <K,T extends BaseEnum<K>> List<K> getCodeList(Class<T> clazz) {
        return Arrays.stream(clazz.getEnumConstants())
                .map(BaseEnum::getCode)
                .collect(Collectors.toList());
    }
    /**
     * 枚举转map
     */
    public static <K,T extends BaseEnum<K>> Map<K,String> toMap(Class<T> clazz) {
        return Arrays.stream(clazz.getEnumConstants())
                .collect(Collectors.toMap(BaseEnum::getCode, BaseEnum::getMsg));
    }
}
