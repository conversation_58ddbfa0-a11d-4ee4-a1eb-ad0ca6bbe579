<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jd.wdy</groupId>
        <artifactId>qf-ai-gateway-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>qf-ai-gateway-common-lang</artifactId>
    <packaging>jar</packaging>

    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-springboot-starter</artifactId>
            <type>pom</type> <!-- type 不能省略 -->
        </dependency>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>qf-ai-gateway-common-pojo</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <!-- jsf -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
        </dependency>
        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
        </dependency>
        <dependency>
            <groupId>com.jd.jr.app</groupId>
            <artifactId>permissions-api</artifactId>
            <version>1.2.7</version>
        </dependency>
    </dependencies>
</project>