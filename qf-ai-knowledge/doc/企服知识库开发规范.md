# 企服知识库开发规范

- 所有maven依赖尽量从根pom取。
- 根pom取不到的话，取最新依赖，先放到根pom管理，然后再引用。
- 代码分包已经建好，请按照每个分包的含义放置代码，如不满足可以新建包。
- 调用顺序一般是controller调用service，service调用dao。其他包按需调用。
- 接口的出入参尽量用对象而不是基本类型，使用lombok可以简化开发。
- 项目使用JDK17开发,启动时需要添加JVM参数: --add-exports java.base/sun.security.action=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/sun.util.calendar=ALL-UNNAMED --add-opens java.base/java.util.concurrent=ALL-UNNAMED --add-opens java.base/java.util.concurrent.locks=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens java.base/jdk.internal.loader=ALL-UNNAMED --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED --add-exports java.base/sun.util.calendar=ALL-UNNAMED --add-opens java.management/com.sun.jmx.mbeanserver=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED  --add-opens java.base/java.time=ALL-UNNAMED --add-opens java.management/java.lang.management=ALL-UNNAMED --add-opens jdk.management/com.sun.management.internal=ALL-UNNAMED --add-opens java.management/sun.management=ALL-UNNAMED --add-opens java.base/sun.net.util=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED  --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED 
