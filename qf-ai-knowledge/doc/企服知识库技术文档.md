# 企服知识库技术文档

# 应用名称

qf-ai-knowledge，以模块的方式先放在qf-ai-gateway中。以后qf-ai-gateway作为中台来建设。

# 模型关系

- 只保留最基础最核心的知识库和分段的模型，不要文档的模型。因为文档这个概念，本质上是一个数据隔离的概念，而数据隔离，可以使用知识库来隔离。更细粒度的隔离，后面再看。
- 在Vearch中，知识库:表空间=1:1，分段:文档=1:1
- 在ES中，我们只用一个索引，里面有知识库ID，分段ID，原文本，元数据json。

# 接口设计

## 接口开放方式

- 全部是http接口

## 鉴权

- 以spring 的aop对controller做拦截。
- 使用appId和secretKey进行鉴权。两者相匹配才能使用所有功能。
- 后续所有的增删改查都要带上appId，以此做数据隔离

## 知识库管理

### 创建空知识库

- 业务方必须先创建一个空知识库，然后在此基础上添加自己的文档。
- 业务方自己进行数据隔离，在qf-ai-knowledge中只提供知识库的顶层模型设计。
- 创建知识库=在mysql中记录一下，哪个租户创建了哪个知识库。后面在知识库里添加文档的时候，首先验证这个知识库是否存在。

### 修改知识库

- 修改名字，描述啥的

### 删除知识库

- 连同其下的分段一同删除

### 查询知识库

- 提供分页查询接口

## 分段管理

### 在知识库中创建分段

- 先在mysql中建立一下知识库和分段1:n的关系。
- 然后在Vearch的表空间中新增一条文档数据，在ES的索引中新增一条数据。这两份数据，都携带了知识库ID、分段ID、元数据...后面可能还有文档ID，但是文档这个概念我们先不引入，后面如果需要，直接在mysql、Vearch、ES中进行扩展即可。
- ES索引的原始查询字段，用text类型存储，中文分词。

### 在知识库中更新分段

- 拿分段ID进行修改，同时修改MySql，ES和Vearch。

### 在知识库中删除分段

- 拿分段ID进行删除

### 分页查询知识库中的分段

- 要传知识库ID，关键词，这里直接查询ES就行，ES没查到可以去Mysql like查询

### 检索知识库

- 此时应当指定，这个分段是用的哪种索引方式：全文检索、向量检索、混合检索。
- 用户传的必须是原始的查询
- 如果是全文检索，直接用ES的term查询。
- 若是向量检索，则直接从向量数据库查。
- 若是混合检索，则两个一起查，取TopK，然后用rerank模型再取TopN。

# SQL设计

## 知识库表

```SQL
CREATE TABLE `knowledge_base` (
  `id` bigint(20) AUTO_INCREMENT NOT NULL COMMENT '自增主键', 
  `base_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '库ID', 
  `appId` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'app ID',
	`name` varchar(256) COLLATE utf8mb4_bin NOT NULL COMMENT '库名称',
  `desc` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '库描述'
  
  `valid` tinyint(11) DEFAULT '1' NOT NULL COMMENT '逻辑删除标志,1:正常记录,0:已逻辑删除', 
  `creator` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人', 
  `modifier` varchar(32) COLLATE utf8mb4_bin NULL COMMENT '最后更新人', 
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
  `modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
  PRIMARY KEY (`id`), 
  KEY `index_appid_base_no` (`appId`,`base_no`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARSET = utf8mb4 COLLATE utf8mb4_bin COMMENT = '知识库表';
```

## 分段表

```SQL
CREATE TABLE `knowledge_segment` (
  `id` bigint(20) AUTO_INCREMENT NOT NULL COMMENT '自增主键', 
  `appId` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'app ID',
  `base_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '库ID', 
  `segment_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '分段ID', 
	`content` text COLLATE utf8mb4_bin NOT NULL COMMENT '分段内容',
  `desc` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分段描述'
  `metadata` json DEFAULT NULL COMMENT '元数据'
  
  `valid` tinyint(11) DEFAULT '1' NOT NULL COMMENT '逻辑删除标志,1:正常记录,0:已逻辑删除', 
  `creator` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人', 
  `modifier` varchar(32) COLLATE utf8mb4_bin NULL COMMENT '最后更新人', 
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
  `modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
  PRIMARY KEY (`id`), 
  KEY `index_appid_segment_no` (`appId`,`segment_no`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARSET = utf8mb4 COLLATE utf8mb4_bin COMMENT = '分段表';
```

# 基础设施

## ES

- 配置已经配好了，你只需要利用RestHighLevelClient去增删改查即可。注意ES的操作放在一个服务中。

```yaml
jes:
  serverUrl: http://es-b8g7p5wl8q-http.es-b8g7p5wl8q.svc.guan-prod.gnhk.x.abchost.local:9200
  appName: wdy-cdp
  secret: 8bf858a8bddce1149c66c869e7316735
```

## Vearch

- 地址如下

```JAVA
final String masterBaseUrl ="http://common-test-master.vectorbase.svc.lf09.n.jd.local";
final String routerBaseUrl ="http://common-test-router.vectorbase.svc.lf09.n.jd.local";
```

- 你需要单独为Vearch的操作搞一个服务。然后封装我们业务所需要的一些方法。具体的API请参考最新vearch文档。

## Embedding模型

- 使用的是基于Xinference的bge-base-zh-v1.5模型。这个已经部署在服务器上了，你只需要调用Xinference的API即可。文档如下：https://inference.readthedocs.io/zh-cn/v1.2.0/models/model_abilities/embed.html

## Rerank模型

- 使用的是基于Xinference的bge-reranker-v2-m3模型。这个已经部署在服务器上了，你只需要调用Xinference的API即可。文档如下：https://inference.readthedocs.io/zh-cn/v1.2.0/models/model_abilities/rerank.html









