# 企服知识库系统

## 项目简介

企服知识库系统是一个基于Spring Boot的知识管理系统，支持知识库和分段的增删改查，以及多种检索方式（全文检索、向量检索、混合检索）。

## 技术架构

- **框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.0+
- **搜索引擎**: Elasticsearch 7.17.9
- **向量数据库**: Vearch
- **向量化模型**: bge-base-zh-v1.5 (基于Xinference)
- **重排序模型**: bge-reranker-v2-m3 (基于Xinference)
- **ORM**: MyBatis Plus 3.5.3.1

## 核心功能

### 知识库管理
- 创建知识库
- 更新知识库信息
- 删除知识库（级联删除分段）
- 查询知识库列表

### 分段管理
- 在知识库中创建分段
- 更新分段内容
- 删除分段
- 查询分段列表

### 知识检索
- **全文检索**: 基于Elasticsearch的文本搜索
- **向量检索**: 基于Vearch的语义相似度搜索
- **混合检索**: 结合全文和向量检索，使用rerank模型重排序

## 数据模型

### 知识库表 (knowledge_base)
- `base_no`: 知识库ID
- `appId`: 应用ID（用于数据隔离）
- `name`: 知识库名称
- `desc`: 知识库描述

### 分段表 (knowledge_segment)
- `segment_no`: 分段ID
- `base_no`: 所属知识库ID
- `content`: 分段内容
- `desc`: 分段描述
- `metadata`: 元数据（JSON格式）

## API接口

### 鉴权方式
所有接口都需要通过appId和secretKey进行鉴权：
- 请求头方式: `X-App-Id` 和 `X-Secret-Key`
- 参数方式: `appId` 和 `secretKey`

### 知识库接口

#### 创建知识库
```
POST /api/knowledge/base/create
Content-Type: application/json

{
    "appId": "test-app",
    "name": "测试知识库",
    "desc": "这是一个测试知识库",
    "creator": "admin"
}
```

#### 查询知识库列表
```
POST /api/knowledge/base/query
Content-Type: application/json

{
    "appId": "test-app",
    "name": "测试",
    "pageNum": 1,
    "pageSize": 10
}
```

### 分段接口

#### 创建分段
```
POST /api/knowledge/segment/create
Content-Type: application/json

{
    "appId": "test-app",
    "baseNo": "KB_123456789",
    "content": "这是分段内容",
    "desc": "分段描述",
    "metadata": "{\"source\": \"manual\"}",
    "creator": "admin"
}
```

#### 检索知识库
```
POST /api/knowledge/segment/search
Content-Type: application/json

{
    "appId": "test-app",
    "baseNo": "KB_123456789",
    "query": "查询内容",
    "searchType": "HYBRID",
    "topK": 10,
    "topN": 5
}
```

检索类型说明：
- `FULL_TEXT`: 全文检索
- `VECTOR`: 向量检索
- `HYBRID`: 混合检索

## 部署说明

### 环境要求
- JDK 17+
- MySQL 8.0+
- Elasticsearch 7.17.9
- Vearch向量数据库
- Xinference服务（用于embedding和rerank）

### 项目结构
企服知识库系统作为qf-ai-gateway项目的一个模块，集成在qf-ai-gateway-starter中启动。

### 配置文件
在 `qf-ai-gateway-starter/src/main/resources/application-{profile}.yml` 中添加配置：

```yaml
# 知识库相关配置
# Elasticsearch配置
jes:
  serverUrl: http://your-es-host:9200
  appName: your-app-name
  secret: your-secret

# Vearch配置
vearch:
  master:
    url: http://your-vearch-master-host
  router:
    url: http://your-vearch-router-host

# Xinference配置
xinference:
  base:
    url: http://your-xinference-host:9997
  embedding:
    model: bge-base-zh-v1.5
  rerank:
    model: bge-reranker-v2-m3
```

### 数据库初始化
执行 `qf-ai-knowledge/src/main/resources/sql/schema.sql` 创建数据表。

### 启动应用
在项目根目录执行：
```bash
cd qf-ai-gateway-starter
mvn spring-boot:run
```

或者启动整个项目：
```bash
mvn clean install
cd qf-ai-gateway-starter
java -jar target/qf-ai-gateway-starter-1.0.0.war
```

## 使用示例

### 1. 创建知识库
```bash
curl -X POST http://localhost:8011/api/knowledge/base/create \
  -H "Content-Type: application/json" \
  -H "X-App-Id: test-app" \
  -H "X-Secret-Key: test-secret" \
  -d '{
    "appId": "test-app",
    "name": "产品手册",
    "desc": "产品使用手册知识库",
    "creator": "admin"
  }'
```

### 2. 添加分段
```bash
curl -X POST http://localhost:8011/api/knowledge/segment/create \
  -H "Content-Type: application/json" \
  -H "X-App-Id: test-app" \
  -H "X-Secret-Key: test-secret" \
  -d '{
    "appId": "test-app",
    "baseNo": "KB_123456789",
    "content": "产品支持多种登录方式，包括用户名密码登录、手机号登录和第三方登录。",
    "desc": "登录方式说明",
    "creator": "admin"
  }'
```

### 3. 检索知识
```bash
curl -X POST http://localhost:8011/api/knowledge/segment/search \
  -H "Content-Type: application/json" \
  -H "X-App-Id: test-app" \
  -H "X-Secret-Key: test-secret" \
  -d '{
    "appId": "test-app",
    "baseNo": "KB_123456789",
    "query": "如何登录系统",
    "searchType": "HYBRID",
    "topK": 10,
    "topN": 5
  }'
```

## 注意事项

1. **数据隔离**: 通过appId实现多租户数据隔离
2. **异步处理**: 分段的向量化和索引是异步处理的
3. **容错机制**: 向量化失败不会影响基本的CRUD操作
4. **性能优化**: 建议为大量数据场景配置合适的ES和Vearch集群

## 开发规范

- 所有maven依赖从根pom获取
- 按照分包结构放置代码
- 调用顺序：controller -> service -> dao
- 接口参数使用对象而非基本类型
- 使用lombok简化开发
