# 企服知识库集成说明

## 概述

企服知识库系统已成功集成到qf-ai-gateway项目中，作为一个独立模块运行。

## 集成架构

```
qf-ai-gateway/
├── qf-ai-gateway-starter/          # 主启动模块
│   ├── config/
│   │   ├── ElasticsearchConfig.java    # ES配置（已存在）
│   │   ├── KnowledgeConfig.java        # 知识库配置
│   │   └── aspect/
│   │       └── KnowledgeAuthAspect.java # 知识库鉴权切面
│   └── Application.java               # 主启动类
├── qf-ai-knowledge/                # 知识库模块
│   ├── controller/                 # 控制器层
│   ├── service/                    # 业务服务层
│   ├── dao/                        # 数据访问层
│   ├── retrive/                    # 检索服务
│   ├── embedding/                  # 向量化服务
│   └── rerank/                     # 重排序服务
└── ...
```

## 主要修改

### 1. 启动类修改
- 在`Application.java`中添加了知识库Mapper扫描路径
- 扫描路径：`com.jd.qf.ai.knowledge.dao`

### 2. 配置集成
- 删除了独立的启动类和配置文件
- 将配置移动到`qf-ai-gateway-starter`的配置文件中
- 复用了现有的Elasticsearch配置

### 3. 依赖管理
- 在根pom.xml中添加了qf-ai-knowledge模块的依赖管理
- 在starter的pom.xml中添加了对qf-ai-knowledge模块的依赖

### 4. 鉴权集成
- 将鉴权切面移动到starter的aspect目录
- 使用DUCC配置中心管理鉴权配置

## 配置说明

### 应用配置
在`application-{profile}.yml`中添加以下配置：

```yaml
# 知识库相关配置
# Elasticsearch配置（复用现有配置）
jes:
  serverUrl: http://es-host:9200
  appName: app-name
  secret: secret-key

# Vearch配置
vearch:
  master:
    url: http://vearch-master-host
  router:
    url: http://vearch-router-host

# Xinference配置
xinference:
  base:
    url: http://xinference-host:9997
  embedding:
    model: bge-base-zh-v1.5
  rerank:
    model: bge-reranker-v2-m3
```

### 鉴权配置
通过DUCC配置中心配置`knowledgeAuthConfig`：

```json
{
  "test-app": "test-secret",
  "prod-app": "prod-secret"
}
```

## 启动方式

### 开发环境
```bash
cd qf-ai-gateway-starter
mvn spring-boot:run -Dspring.profiles.active=local
```

### 生产环境
```bash
mvn clean install
cd qf-ai-gateway-starter
java -jar target/qf-ai-gateway-starter-1.0.0.war --spring.profiles.active=prod
```

## API访问

知识库API通过主应用端口访问：
- 默认端口：8011
- API前缀：`/api/knowledge/`

示例：
```
POST http://localhost:8011/api/knowledge/base/create
POST http://localhost:8011/api/knowledge/segment/search
```

## 数据库初始化

执行以下SQL脚本创建知识库相关表：
```sql
-- 位置：qf-ai-knowledge/src/main/resources/sql/schema.sql
-- 包含：knowledge_base表和knowledge_segment表
```

## 注意事项

1. **配置复用**：复用了现有的Elasticsearch配置，避免重复配置
2. **包扫描**：确保知识库相关的包被正确扫描到
3. **依赖管理**：通过根pom统一管理依赖版本
4. **鉴权集成**：使用统一的鉴权机制和配置中心
5. **端口统一**：使用主应用的端口，无需额外端口配置

## 验证集成

启动应用后，可以通过以下方式验证集成是否成功：

1. 检查日志中是否有知识库相关的Bean初始化信息
2. 访问健康检查端点：`GET /actuator/health`
3. 调用知识库API进行功能测试

## 后续扩展

如需添加新的知识库功能：
1. 在qf-ai-knowledge模块中添加相关代码
2. 如需新的配置，在starter的配置文件中添加
3. 如需新的切面或配置类，在starter的config目录中添加
