-- 企服知识库数据库初始化脚本

-- 创建知识库表
CREATE TABLE IF NOT EXISTS `knowledge_base` (
  `id` bigint(20) AUTO_INCREMENT NOT NULL COMMENT '自增主键', 
  `base_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '库ID', 
  `appId` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'app ID',
  `name` varchar(256) COLLATE utf8mb4_bin NOT NULL COMMENT '库名称',
  `desc` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '库描述',
  
  `valid` tinyint(11) DEFAULT '1' NOT NULL COMMENT '逻辑删除标志,1:正常记录,0:已逻辑删除', 
  `creator` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人', 
  `modifier` varchar(32) COLLATE utf8mb4_bin NULL COMMENT '最后更新人', 
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
  `modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
  PRIMARY KEY (`id`), 
  UNIQUE KEY `uk_appid_base_no` (`appId`,`base_no`),
  KEY `index_appid_base_no` (`appId`,`base_no`),
  KEY `index_created_time` (`created_time`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARSET = utf8mb4 COLLATE utf8mb4_bin COMMENT = '知识库表';

-- 创建分段表
CREATE TABLE IF NOT EXISTS `knowledge_segment` (
  `id` bigint(20) AUTO_INCREMENT NOT NULL COMMENT '自增主键', 
  `appId` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'app ID',
  `base_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '库ID', 
  `segment_no` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '分段ID', 
  `content` text COLLATE utf8mb4_bin NOT NULL COMMENT '分段内容',
  `desc` varchar(512) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分段描述',
  `metadata` json DEFAULT NULL COMMENT '元数据',
  
  `valid` tinyint(11) DEFAULT '1' NOT NULL COMMENT '逻辑删除标志,1:正常记录,0:已逻辑删除', 
  `creator` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '创建人', 
  `modifier` varchar(32) COLLATE utf8mb4_bin NULL COMMENT '最后更新人', 
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
  `modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
  PRIMARY KEY (`id`), 
  UNIQUE KEY `uk_appid_segment_no` (`appId`,`segment_no`),
  KEY `index_appid_segment_no` (`appId`,`segment_no`),
  KEY `index_appid_base_no` (`appId`,`base_no`),
  KEY `index_created_time` (`created_time`),
  FULLTEXT KEY `ft_content` (`content`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARSET = utf8mb4 COLLATE utf8mb4_bin COMMENT = '分段表';
