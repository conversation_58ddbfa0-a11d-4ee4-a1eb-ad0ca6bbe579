<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.knowledge.dao.KnowledgeSegmentMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jd.qf.ai.knowledge.common.entity.KnowledgeSegment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="appId" property="appId" jdbcType="VARCHAR"/>
        <result column="base_no" property="baseNo" jdbcType="VARCHAR"/>
        <result column="segment_no" property="segmentNo" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="desc" property="desc" jdbcType="VARCHAR"/>
        <result column="metadata" property="metadata" jdbcType="LONGVARCHAR"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, appId, base_no, segment_no, content, `desc`, metadata, valid, creator, modifier, created_time, modified_time
    </sql>

    <!-- 根据appId和segmentNo查询分段 -->
    <select id="selectByAppIdAndSegmentNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_segment
        WHERE appId = #{appId} AND segment_no = #{segmentNo} AND valid = 1
    </select>

    <!-- 根据知识库ID分页查询分段 -->
    <select id="selectPageByBaseNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_segment
        WHERE appId = #{appId} AND base_no = #{baseNo} AND valid = 1
        <if test="keyword != null and keyword != ''">
            AND (content LIKE CONCAT('%', #{keyword}, '%') OR `desc` LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据知识库ID查询所有分段 -->
    <select id="selectByBaseNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_segment
        WHERE appId = #{appId} AND base_no = #{baseNo} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据知识库ID删除所有分段 -->
    <update id="deleteByBaseNo">
        UPDATE knowledge_segment
        SET valid = 0, modified_time = NOW()
        WHERE appId = #{appId} AND base_no = #{baseNo} AND valid = 1
    </update>

    <!-- 检查分段是否存在 -->
    <select id="existsByAppIdAndSegmentNo" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM knowledge_segment
        WHERE appId = #{appId} AND segment_no = #{segmentNo} AND valid = 1
    </select>

    <!-- 根据内容模糊查询分段 -->
    <select id="selectByContentLike" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_segment
        WHERE appId = #{appId} AND base_no = #{baseNo} AND valid = 1
        AND content LIKE CONCAT('%', #{content}, '%')
        ORDER BY created_time DESC
    </select>

</mapper>
