<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.knowledge.dao.KnowledgeBaseMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.jd.qf.ai.knowledge.common.entity.KnowledgeBase">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="base_no" property="baseNo" jdbcType="VARCHAR"/>
        <result column="appId" property="appId" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="desc" property="desc" jdbcType="VARCHAR"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, base_no, appId, name, `desc`, valid, creator, modifier, created_time, modified_time
    </sql>

    <!-- 根据appId和baseNo查询知识库 -->
    <select id="selectByAppIdAndBaseNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_base
        WHERE appId = #{appId} AND base_no = #{baseNo} AND valid = 1
    </select>

    <!-- 根据appId分页查询知识库 -->
    <select id="selectPageByAppId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_base
        WHERE appId = #{appId} AND valid = 1
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据appId查询知识库列表 -->
    <select id="selectByAppId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM knowledge_base
        WHERE appId = #{appId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 检查知识库是否存在 -->
    <select id="existsByAppIdAndBaseNo" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM knowledge_base
        WHERE appId = #{appId} AND base_no = #{baseNo} AND valid = 1
    </select>

</mapper>
