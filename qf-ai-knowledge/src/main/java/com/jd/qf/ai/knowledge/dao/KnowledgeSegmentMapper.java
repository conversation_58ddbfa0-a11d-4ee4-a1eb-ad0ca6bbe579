package com.jd.qf.ai.knowledge.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.qf.ai.knowledge.common.entity.KnowledgeSegment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识分段Mapper接口
 * <AUTHOR>
 * @date 2025-01-02
 */
@Mapper
public interface KnowledgeSegmentMapper extends BaseMapper<KnowledgeSegment> {

    /**
     * 根据appId和segmentNo查询分段
     * @param appId 应用ID
     * @param segmentNo 分段ID
     * @return 分段信息
     */
    KnowledgeSegment selectByAppIdAndSegmentNo(@Param("appId") String appId, @Param("segmentNo") String segmentNo);

    /**
     * 根据知识库ID分页查询分段
     * @param page 分页参数
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @param keyword 关键词(模糊查询)
     * @return 分段列表
     */
    IPage<KnowledgeSegment> selectPageByBaseNo(Page<KnowledgeSegment> page, 
                                               @Param("appId") String appId, 
                                               @Param("baseNo") String baseNo, 
                                               @Param("keyword") String keyword);

    /**
     * 根据知识库ID查询所有分段
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @return 分段列表
     */
    List<KnowledgeSegment> selectByBaseNo(@Param("appId") String appId, @Param("baseNo") String baseNo);

    /**
     * 根据知识库ID删除所有分段
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @return 删除数量
     */
    int deleteByBaseNo(@Param("appId") String appId, @Param("baseNo") String baseNo);

    /**
     * 检查分段是否存在
     * @param appId 应用ID
     * @param segmentNo 分段ID
     * @return 是否存在
     */
    boolean existsByAppIdAndSegmentNo(@Param("appId") String appId, @Param("segmentNo") String segmentNo);

    /**
     * 根据内容模糊查询分段
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @param content 内容关键词
     * @return 分段列表
     */
    List<KnowledgeSegment> selectByContentLike(@Param("appId") String appId, 
                                               @Param("baseNo") String baseNo, 
                                               @Param("content") String content);
}
