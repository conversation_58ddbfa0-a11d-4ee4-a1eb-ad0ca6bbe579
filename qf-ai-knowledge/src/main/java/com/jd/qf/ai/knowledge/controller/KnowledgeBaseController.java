package com.jd.qf.ai.knowledge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.qf.ai.knowledge.common.annotation.ValidateAuth;
import com.jd.qf.ai.knowledge.common.dto.KnowledgeBaseDTO;
import com.jd.qf.ai.knowledge.common.dto.request.CreateKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.dto.request.QueryKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.dto.request.UpdateKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.service.KnowledgeBaseService;
import com.jdt.open.dto.GeneralResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 知识库控制器
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/api/knowledge/base")
@ValidateAuth
@Slf4j
public class KnowledgeBaseController {

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    /**
     * 创建知识库
     */
    @PostMapping("/create")
    public GeneralResponse<KnowledgeBaseDTO> createKnowledgeBase(@Valid @RequestBody CreateKnowledgeBaseRequest request) {
        try {
            KnowledgeBaseDTO result = knowledgeBaseService.createKnowledgeBase(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("创建知识库失败", e);
            return GeneralResponse.error("创建知识库失败: " + e.getMessage());
        }
    }

    /**
     * 更新知识库
     */
    @PostMapping("/update")
    public GeneralResponse<Boolean> updateKnowledgeBase(@Valid @RequestBody UpdateKnowledgeBaseRequest request) {
        try {
            boolean result = knowledgeBaseService.updateKnowledgeBase(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("更新知识库失败", e);
            return GeneralResponse.error("更新知识库失败: " + e.getMessage());
        }
    }

    /**
     * 删除知识库
     */
    @PostMapping("/delete")
    public GeneralResponse<Boolean> deleteKnowledgeBase(@RequestParam String appId,
                                                       @RequestParam String baseNo,
                                                       @RequestParam String modifier) {
        try {
            boolean result = knowledgeBaseService.deleteKnowledgeBase(appId, baseNo, modifier);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("删除知识库失败", e);
            return GeneralResponse.error("删除知识库失败: " + e.getMessage());
        }
    }

    /**
     * 查询知识库详情
     */
    @GetMapping("/detail")
    public GeneralResponse<KnowledgeBaseDTO> getKnowledgeBase(@RequestParam String appId,
                                                             @RequestParam String baseNo) {
        try {
            KnowledgeBaseDTO result = knowledgeBaseService.getKnowledgeBase(appId, baseNo);
            if (result == null) {
                return GeneralResponse.error("知识库不存在");
            }
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("查询知识库详情失败", e);
            return GeneralResponse.error("查询知识库详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询知识库
     */
    @PostMapping("/query")
    public GeneralResponse<IPage<KnowledgeBaseDTO>> queryKnowledgeBases(@Valid @RequestBody QueryKnowledgeBaseRequest request) {
        try {
            IPage<KnowledgeBaseDTO> result = knowledgeBaseService.queryKnowledgeBases(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("分页查询知识库失败", e);
            return GeneralResponse.error("分页查询知识库失败: " + e.getMessage());
        }
    }

    /**
     * 检查知识库是否存在
     */
    @GetMapping("/exists")
    public GeneralResponse<Boolean> existsKnowledgeBase(@RequestParam String appId,
                                                       @RequestParam String baseNo) {
        try {
            boolean result = knowledgeBaseService.existsKnowledgeBase(appId, baseNo);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("检查知识库是否存在失败", e);
            return GeneralResponse.error("检查知识库是否存在失败: " + e.getMessage());
        }
    }
}
