package com.jd.qf.ai.knowledge.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 知识库DTO
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class KnowledgeBaseDTO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 库ID
     */
    private String baseNo;

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 256, message = "知识库名称长度不能超过256个字符")
    private String name;

    /**
     * 库描述
     */
    @Size(max = 512, message = "知识库描述长度不能超过512个字符")
    private String desc;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 最后更新人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifiedTime;
}
