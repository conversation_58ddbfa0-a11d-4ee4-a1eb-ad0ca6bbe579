package com.jd.qf.ai.knowledge.common.dto.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 查询分段请求
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class QuerySegmentRequest {

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 库ID
     */
    @NotBlank(message = "知识库ID不能为空")
    private String baseNo;

    /**
     * 关键词(模糊查询)
     */
    private String keyword;

    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;
}
