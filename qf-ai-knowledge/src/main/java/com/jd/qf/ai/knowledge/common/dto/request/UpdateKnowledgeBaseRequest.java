package com.jd.qf.ai.knowledge.common.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新知识库请求
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class UpdateKnowledgeBaseRequest {

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 库ID
     */
    @NotBlank(message = "知识库ID不能为空")
    private String baseNo;

    /**
     * 库名称
     */
    @Size(max = 256, message = "知识库名称长度不能超过256个字符")
    private String name;

    /**
     * 库描述
     */
    @Size(max = 512, message = "知识库描述长度不能超过512个字符")
    private String desc;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String modifier;
}
