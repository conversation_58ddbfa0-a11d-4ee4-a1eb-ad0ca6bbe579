package com.jd.qf.ai.knowledge.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检索类型枚举
 * <AUTHOR>
 * @date 2025-01-02
 */
@Getter
@AllArgsConstructor
public enum SearchTypeEnum {

    /**
     * 全文检索
     */
    FULL_TEXT("FULL_TEXT", "全文检索"),

    /**
     * 向量检索
     */
    VECTOR("VECTOR", "向量检索"),

    /**
     * 混合检索
     */
    HYBRID("HYBRID", "混合检索");

    /**
     * 类型code
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static SearchTypeEnum getByCode(String code) {
        for (SearchTypeEnum typeEnum : SearchTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 验证code是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
