package com.jd.qf.ai.knowledge.common.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新分段请求
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class UpdateSegmentRequest {

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 分段ID
     */
    @NotBlank(message = "分段ID不能为空")
    private String segmentNo;

    /**
     * 分段内容
     */
    private String content;

    /**
     * 分段描述
     */
    @Size(max = 512, message = "分段描述长度不能超过512个字符")
    private String desc;

    /**
     * 元数据(JSON格式)
     */
    private String metadata;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String modifier;
}
