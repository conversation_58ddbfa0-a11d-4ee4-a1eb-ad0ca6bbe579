package com.jd.qf.ai.knowledge.common.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Map;

/**
 * 创建分段请求
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class CreateSegmentRequest {

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 库ID
     */
    @NotBlank(message = "知识库ID不能为空")
    private String baseNo;

    /**
     * 分段内容
     */
    @NotBlank(message = "分段内容不能为空")
    private String content;

    /**
     * 分段描述
     */
    @Size(max = 512, message = "分段描述长度不能超过512个字符")
    private String desc;

    /**
     * 元数据(JSON格式)
     */
    private Map<String,Object> metadata;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String creator;
}
