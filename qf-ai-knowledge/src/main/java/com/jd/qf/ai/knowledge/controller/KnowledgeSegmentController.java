package com.jd.qf.ai.knowledge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.qf.ai.knowledge.common.annotation.ValidateAuth;
import com.jd.qf.ai.knowledge.common.dto.KnowledgeSegmentDTO;
import com.jd.qf.ai.knowledge.common.dto.request.CreateSegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.request.QuerySegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.request.SearchKnowledgeRequest;
import com.jd.qf.ai.knowledge.common.dto.request.UpdateSegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.response.SearchResult;
import com.jd.qf.ai.knowledge.service.KnowledgeSegmentService;
import com.jdt.open.dto.GeneralResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 知识分段控制器
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/api/knowledge/segment")
@ValidateAuth
@Slf4j
public class KnowledgeSegmentController {

    @Autowired
    private KnowledgeSegmentService knowledgeSegmentService;

    /**
     * 创建分段
     */
    @PostMapping("/create")
    public GeneralResponse<KnowledgeSegmentDTO> createSegment(@Valid @RequestBody CreateSegmentRequest request) {
        try {
            KnowledgeSegmentDTO result = knowledgeSegmentService.createSegment(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("创建分段失败", e);
            return GeneralResponse.error("创建分段失败: " + e.getMessage());
        }
    }

    /**
     * 更新分段
     */
    @PostMapping("/update")
    public GeneralResponse<Boolean> updateSegment(@Valid @RequestBody UpdateSegmentRequest request) {
        try {
            boolean result = knowledgeSegmentService.updateSegment(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("更新分段失败", e);
            return GeneralResponse.error("更新分段失败: " + e.getMessage());
        }
    }

    /**
     * 删除分段
     */
    @PostMapping("/delete")
    public GeneralResponse<Boolean> deleteSegment(@RequestParam String appId,
                                                 @RequestParam String segmentNo,
                                                 @RequestParam String modifier) {
        try {
            boolean result = knowledgeSegmentService.deleteSegment(appId, segmentNo, modifier);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("删除分段失败", e);
            return GeneralResponse.error("删除分段失败: " + e.getMessage());
        }
    }

    /**
     * 查询分段详情
     */
    @GetMapping("/detail")
    public GeneralResponse<KnowledgeSegmentDTO> getSegment(@RequestParam String appId,
                                                          @RequestParam String segmentNo) {
        try {
            KnowledgeSegmentDTO result = knowledgeSegmentService.getSegment(appId, segmentNo);
            if (result == null) {
                return GeneralResponse.error("分段不存在");
            }
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("查询分段详情失败", e);
            return GeneralResponse.error("查询分段详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询分段
     */
    @PostMapping("/query")
    public GeneralResponse<IPage<KnowledgeSegmentDTO>> querySegments(@Valid @RequestBody QuerySegmentRequest request) {
        try {
            IPage<KnowledgeSegmentDTO> result = knowledgeSegmentService.querySegments(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("分页查询分段失败", e);
            return GeneralResponse.error("分页查询分段失败: " + e.getMessage());
        }
    }

    /**
     * 检索知识库
     */
    @PostMapping("/search")
    public GeneralResponse<List<SearchResult>> searchKnowledge(@Valid @RequestBody SearchKnowledgeRequest request) {
        try {
            List<SearchResult> result = knowledgeSegmentService.searchKnowledge(request);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("检索知识库失败", e);
            return GeneralResponse.error("检索知识库失败: " + e.getMessage());
        }
    }

    /**
     * 检查分段是否存在
     */
    @GetMapping("/exists")
    public GeneralResponse<Boolean> existsSegment(@RequestParam String appId,
                                                 @RequestParam String segmentNo) {
        try {
            boolean result = knowledgeSegmentService.existsSegment(appId, segmentNo);
            return GeneralResponse.success(result);
        } catch (Exception e) {
            log.error("检查分段是否存在失败", e);
            return GeneralResponse.error("检查分段是否存在失败: " + e.getMessage());
        }
    }
}
