package com.jd.qf.ai.knowledge.retrive;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jd.qf.ai.knowledge.common.dto.response.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Vearch向量数据库服务
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
@Slf4j
public class VearchService {

    @Value("${vearch.master.url:http://common-test-master.vectorbase.svc.lf09.n.jd.local}")
    private String masterBaseUrl;

    @Value("${vearch.router.url:http://common-test-router.vectorbase.svc.lf09.n.jd.local}")
    private String routerBaseUrl;

    /**
     * 创建表空间(知识库)
     */
    public boolean createSpace(String appId, String baseNo) {
        try {
            String spaceName = generateSpaceName(appId, baseNo);
            
            JSONObject spaceConfig = new JSONObject();
            spaceConfig.set("name", spaceName);
            spaceConfig.set("partition_num", 1);
            spaceConfig.set("replica_num", 1);
            
            // 定义字段结构
            JSONObject properties = new JSONObject();
            
            // 分段ID字段
            JSONObject segmentNoField = new JSONObject();
            segmentNoField.set("type", "keyword");
            segmentNoField.set("index", true);
            properties.set("segmentNo", segmentNoField);
            
            // 内容字段
            JSONObject contentField = new JSONObject();
            contentField.set("type", "text");
            contentField.set("index", true);
            properties.set("content", contentField);
            
            // 向量字段
            JSONObject vectorField = new JSONObject();
            vectorField.set("type", "vector");
            vectorField.set("dimension", 768); // bge-base-zh-v1.5的向量维度
            vectorField.set("index", true);
            vectorField.set("store_type", "MemoryOnly");
            vectorField.set("store_param", new JSONObject().set("cache_size", 2048));
            properties.set("vector", vectorField);
            
            spaceConfig.set("properties", properties);

            String url = masterBaseUrl + "/space/" + spaceName;
            HttpResponse response = HttpRequest.put(url)
                    .header("Content-Type", "application/json")
                    .body(spaceConfig.toString())
                    .execute();

            if (response.getStatus() == 200) {
                log.info("成功创建Vearch表空间: spaceName={}", spaceName);
                return true;
            } else {
                log.error("创建Vearch表空间失败: spaceName={}, status={}, body={}", spaceName, response.getStatus(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("创建Vearch表空间异常: appId={}, baseNo={}", appId, baseNo, e);
            return false;
        }
    }

    /**
     * 添加文档到Vearch
     */
    public boolean addDocument(String appId, String baseNo, String segmentNo, String content, double[] vector, String metadata) {
        try {
            String spaceName = generateSpaceName(appId, baseNo);
            
            JSONObject document = new JSONObject();
            document.set("segmentNo", segmentNo);
            document.set("content", content);
            document.set("vector", vector);
            document.set("metadata", metadata);

            String url = routerBaseUrl + "/" + spaceName + "/_doc";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(document.toString())
                    .execute();

            if (response.getStatus() == 200) {
                log.info("成功添加文档到Vearch: spaceName={}, segmentNo={}", spaceName, segmentNo);
                return true;
            } else {
                log.error("添加文档到Vearch失败: spaceName={}, segmentNo={}, status={}, body={}", 
                         spaceName, segmentNo, response.getStatus(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("添加文档到Vearch异常: appId={}, baseNo={}, segmentNo={}", appId, baseNo, segmentNo, e);
            return false;
        }
    }

    /**
     * 更新Vearch中的文档
     */
    public boolean updateDocument(String appId, String baseNo, String segmentNo, String content, double[] vector, String metadata) {
        try {
            String spaceName = generateSpaceName(appId, baseNo);
            
            JSONObject document = new JSONObject();
            if (StrUtil.isNotBlank(content)) {
                document.set("content", content);
            }
            if (vector != null && vector.length > 0) {
                document.set("vector", vector);
            }
            if (StrUtil.isNotBlank(metadata)) {
                document.set("metadata", metadata);
            }

            String url = routerBaseUrl + "/" + spaceName + "/_update/" + segmentNo;
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(document.toString())
                    .execute();

            if (response.getStatus() == 200) {
                log.info("成功更新Vearch文档: spaceName={}, segmentNo={}", spaceName, segmentNo);
                return true;
            } else {
                log.error("更新Vearch文档失败: spaceName={}, segmentNo={}, status={}, body={}", 
                         spaceName, segmentNo, response.getStatus(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("更新Vearch文档异常: appId={}, baseNo={}, segmentNo={}", appId, baseNo, segmentNo, e);
            return false;
        }
    }

    /**
     * 从Vearch删除文档
     */
    public boolean deleteDocument(String appId, String baseNo, String segmentNo) {
        try {
            String spaceName = generateSpaceName(appId, baseNo);
            
            String url = routerBaseUrl + "/" + spaceName + "/_doc/" + segmentNo;
            HttpResponse response = HttpRequest.delete(url).execute();

            if (response.getStatus() == 200) {
                log.info("成功从Vearch删除文档: spaceName={}, segmentNo={}", spaceName, segmentNo);
                return true;
            } else {
                log.error("从Vearch删除文档失败: spaceName={}, segmentNo={}, status={}, body={}", 
                         spaceName, segmentNo, response.getStatus(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("从Vearch删除文档异常: appId={}, baseNo={}, segmentNo={}", appId, baseNo, segmentNo, e);
            return false;
        }
    }

    /**
     * 向量检索
     */
    public List<SearchResult> vectorSearch(String appId, String baseNo, double[] queryVector, int topK) {
        try {
            String spaceName = generateSpaceName(appId, baseNo);
            
            JSONObject query = new JSONObject();
            JSONObject vectorQuery = new JSONObject();
            vectorQuery.set("field", "vector");
            vectorQuery.set("feature", queryVector);
            
            query.set("vector", JSONUtil.createArray().set(vectorQuery));
            query.set("size", topK);
            query.set("fields", JSONUtil.createArray().set("segmentNo").set("content").set("metadata"));

            String url = routerBaseUrl + "/" + spaceName + "/_search";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(query.toString())
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray hits = result.getJSONObject("hits").getJSONArray("hits");
                
                List<SearchResult> results = new ArrayList<>();
                for (int i = 0; i < hits.size(); i++) {
                    JSONObject hit = hits.getJSONObject(i);
                    JSONObject source = hit.getJSONObject("_source");
                    
                    SearchResult searchResult = new SearchResult();
                    searchResult.setSegmentNo(source.getStr("segmentNo"));
                    searchResult.setContent(source.getStr("content"));
                    searchResult.setMetadata(source.getStr("metadata"));
                    searchResult.setScore(hit.getDouble("_score"));
                    searchResult.setSearchType("VECTOR");
                    results.add(searchResult);
                }
                
                log.info("向量检索完成: spaceName={}, 结果数量={}", spaceName, results.size());
                return results;
            } else {
                log.error("向量检索失败: spaceName={}, status={}, body={}", spaceName, response.getStatus(), response.body());
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("向量检索异常: appId={}, baseNo={}", appId, baseNo, e);
            return new ArrayList<>();
        }
    }

    /**
     * 删除表空间
     */
    public boolean deleteSpace(String appId, String baseNo) {
        try {
            String spaceName = generateSpaceName(appId, baseNo);
            
            String url = masterBaseUrl + "/space/" + spaceName;
            HttpResponse response = HttpRequest.delete(url).execute();

            if (response.getStatus() == 200) {
                log.info("成功删除Vearch表空间: spaceName={}", spaceName);
                return true;
            } else {
                log.error("删除Vearch表空间失败: spaceName={}, status={}, body={}", spaceName, response.getStatus(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("删除Vearch表空间异常: appId={}, baseNo={}", appId, baseNo, e);
            return false;
        }
    }

    /**
     * 生成表空间名称
     */
    private String generateSpaceName(String appId, String baseNo) {
        return appId + "_" + baseNo;
    }
}
