package com.jd.qf.ai.knowledge.rerank;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jd.qf.ai.knowledge.common.dto.response.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Rerank服务 - 基于Xinference的bge-reranker-v2-m3模型
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
@Slf4j
public class RerankService {

    @Value("${xinference.base.url:http://xinference-server:9997}")
    private String xinferenceBaseUrl;

    @Value("${xinference.rerank.model:bge-reranker-v2-m3}")
    private String rerankModel;

    /**
     * 对搜索结果进行重排序
     * @param query 查询文本
     * @param searchResults 搜索结果列表
     * @param topN 返回前N个结果
     * @return 重排序后的结果
     */
    public List<SearchResult> rerank(String query, List<SearchResult> searchResults, int topN) {
        if (searchResults == null || searchResults.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 准备文档列表
            List<String> documents = searchResults.stream()
                    .map(SearchResult::getContent)
                    .collect(Collectors.toList());

            JSONObject request = new JSONObject();
            request.put("model", rerankModel);
            request.put("query", query);
            request.put("documents", documents);
            request.put("top_n", Math.min(topN, searchResults.size()));

            String url = xinferenceBaseUrl + "/v1/rerank";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(request.toString())
                    .timeout(30000) // 30秒超时
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray results = result.getJSONArray("results");
                
                List<SearchResult> rerankedResults = new ArrayList<>();
                for (int i = 0; i < results.size(); i++) {
                    JSONObject item = results.getJSONObject(i);
                    int index = item.getInt("index");
                    double score = item.getDouble("relevance_score");
                    
                    if (index < searchResults.size()) {
                        SearchResult originalResult = searchResults.get(index);
                        SearchResult rerankedResult = new SearchResult();
                        rerankedResult.setSegmentNo(originalResult.getSegmentNo());
                        rerankedResult.setContent(originalResult.getContent());
                        rerankedResult.setDesc(originalResult.getDesc());
                        rerankedResult.setMetadata(originalResult.getMetadata());
                        rerankedResult.setScore(score); // 使用rerank分数
                        rerankedResult.setSearchType("HYBRID");
                        rerankedResults.add(rerankedResult);
                    }
                }
                
                log.info("重排序完成: query={}, 原始结果数={}, 重排序结果数={}", 
                        query.substring(0, Math.min(50, query.length())), searchResults.size(), rerankedResults.size());
                return rerankedResults;
            }
            
            log.error("重排序失败: query={}, status={}, body={}", 
                     query.substring(0, Math.min(50, query.length())), response.getStatus(), response.body());
            
            // 如果rerank失败，返回原始结果的前topN个
            return searchResults.stream()
                    .limit(topN)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("重排序异常: query={}", query.substring(0, Math.min(50, query.length())), e);
            
            // 如果rerank异常，返回原始结果的前topN个
            return searchResults.stream()
                    .limit(topN)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 计算查询与文档的相关性分数
     * @param query 查询文本
     * @param document 文档文本
     * @return 相关性分数
     */
    public double calculateRelevanceScore(String query, String document) {
        try {
            JSONObject request = new JSONObject();
            request.put("model", rerankModel);
            request.put("query", query);
            request.put("documents", JSONUtil.createArray().put(document));
            request.put("top_n", 1);

            String url = xinferenceBaseUrl + "/v1/rerank";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(request.toString())
                    .timeout(15000) // 15秒超时
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray results = result.getJSONArray("results");
                
                if (results.size() > 0) {
                    JSONObject item = results.getJSONObject(0);
                    double score = item.getDouble("relevance_score");
                    
                    log.debug("计算相关性分数完成: query={}, score={}", 
                            query.substring(0, Math.min(30, query.length())), score);
                    return score;
                }
            }
            
            log.error("计算相关性分数失败: query={}, status={}, body={}", 
                     query.substring(0, Math.min(30, query.length())), response.getStatus(), response.body());
            return 0.0;
            
        } catch (Exception e) {
            log.error("计算相关性分数异常: query={}", query.substring(0, Math.min(30, query.length())), e);
            return 0.0;
        }
    }

    /**
     * 检查Rerank服务是否可用
     * @return 是否可用
     */
    public boolean isServiceAvailable() {
        try {
            String url = xinferenceBaseUrl + "/v1/models";
            HttpResponse response = HttpRequest.get(url)
                    .timeout(5000) // 5秒超时
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray data = result.getJSONArray("data");
                
                // 检查是否包含我们需要的模型
                for (int i = 0; i < data.size(); i++) {
                    JSONObject model = data.getJSONObject(i);
                    if (rerankModel.equals(model.getStr("id"))) {
                        log.info("Rerank服务可用: model={}", rerankModel);
                        return true;
                    }
                }
                
                log.warn("Rerank服务中未找到指定模型: model={}", rerankModel);
                return false;
            }
            
            log.error("Rerank服务不可用: status={}", response.getStatus());
            return false;
        } catch (Exception e) {
            log.error("检查Rerank服务可用性异常", e);
            return false;
        }
    }
}
