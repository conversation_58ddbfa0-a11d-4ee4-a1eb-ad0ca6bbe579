package com.jd.qf.ai.knowledge.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.qf.ai.knowledge.common.entity.KnowledgeBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识库Mapper接口
 * <AUTHOR>
 * @date 2025-01-02
 */
@Mapper
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {

    /**
     * 根据appId和baseNo查询知识库
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @return 知识库信息
     */
    KnowledgeBase selectByAppIdAndBaseNo(@Param("appId") String appId, @Param("baseNo") String baseNo);

    /**
     * 根据appId分页查询知识库
     * @param page 分页参数
     * @param appId 应用ID
     * @param name 知识库名称(模糊查询)
     * @return 知识库列表
     */
    IPage<KnowledgeBase> selectPageByAppId(Page<KnowledgeBase> page, @Param("appId") String appId, @Param("name") String name);

    /**
     * 根据appId查询知识库列表
     * @param appId 应用ID
     * @return 知识库列表
     */
    List<KnowledgeBase> selectByAppId(@Param("appId") String appId);

    /**
     * 检查知识库是否存在
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @return 是否存在
     */
    boolean existsByAppIdAndBaseNo(@Param("appId") String appId, @Param("baseNo") String baseNo);
}
