package com.jd.qf.ai.knowledge.common.dto.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 知识库检索请求
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class SearchKnowledgeRequest {

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 库ID
     */
    @NotBlank(message = "知识库ID不能为空")
    private String baseNo;

    /**
     * 查询内容
     */
    @NotBlank(message = "查询内容不能为空")
    private String query;

    /**
     * 检索类型：FULL_TEXT(全文检索)、VECTOR(向量检索)、HYBRID(混合检索)
     */
    @NotBlank(message = "检索类型不能为空")
    private String searchType;

    /**
     * 返回结果数量，默认10
     */
    @Min(value = 1, message = "返回结果数量至少为1")
    @Max(value = 100, message = "返回结果数量最多为100")
    private Integer topK = 10;

    /**
     * 混合检索时rerank后的结果数量，默认5
     */
    @Min(value = 1, message = "rerank结果数量至少为1")
    @Max(value = 50, message = "rerank结果数量最多为50")
    private Integer topN = 5;
}
