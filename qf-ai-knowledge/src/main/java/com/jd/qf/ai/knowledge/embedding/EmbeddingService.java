package com.jd.qf.ai.knowledge.embedding;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Embedding服务 - 基于Xinference的bge-base-zh-v1.5模型
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
@Slf4j
public class EmbeddingService {

    @Value("${xinference.base.url:http://xinference-server:9997}")
    private String xinferenceBaseUrl;

    @Value("${xinference.embedding.model:bge-base-zh-v1.5}")
    private String embeddingModel;

    /**
     * 获取文本的向量表示
     * @param text 输入文本
     * @return 向量数组
     */
    public double[] getEmbedding(String text) {
        try {
            JSONObject request = new JSONObject();
            request.put("model", embeddingModel);
            request.put("input", text);

            String url = xinferenceBaseUrl + "/v1/embeddings";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(request.toString())
                    .timeout(30000) // 30秒超时
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray data = result.getJSONArray("data");
                if (data != null && data.size() > 0) {
                    JSONObject firstItem = data.getJSONObject(0);
                    JSONArray embedding = firstItem.getJSONArray("embedding");
                    
                    double[] vector = new double[embedding.size()];
                    for (int i = 0; i < embedding.size(); i++) {
                        vector[i] = embedding.getDouble(i);
                    }
                    
                    log.debug("成功获取文本向量: text={}, vector_dim={}", text.substring(0, Math.min(50, text.length())), vector.length);
                    return vector;
                }
            }
            
            log.error("获取文本向量失败: text={}, status={}, body={}", 
                     text.substring(0, Math.min(50, text.length())), response.getStatus(), response.body());
            return null;
        } catch (Exception e) {
            log.error("获取文本向量异常: text={}", text.substring(0, Math.min(50, text.length())), e);
            return null;
        }
    }

    /**
     * 批量获取文本的向量表示
     * @param texts 输入文本列表
     * @return 向量数组列表
     */
    public List<double[]> getBatchEmbeddings(List<String> texts) {
        try {
            JSONObject request = new JSONObject();
            request.put("model", embeddingModel);
            request.put("input", texts);

            String url = xinferenceBaseUrl + "/v1/embeddings";
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .body(request.toString())
                    .timeout(60000) // 60秒超时
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray data = result.getJSONArray("data");
                
                List<double[]> vectors = new ArrayList<>();
                for (int i = 0; i < data.size(); i++) {
                    JSONObject item = data.getJSONObject(i);
                    JSONArray embedding = item.getJSONArray("embedding");
                    
                    double[] vector = new double[embedding.size()];
                    for (int j = 0; j < embedding.size(); j++) {
                        vector[j] = embedding.getDouble(j);
                    }
                    vectors.add(vector);
                }
                
                log.info("成功批量获取文本向量: texts_count={}, vector_dim={}", texts.size(), 
                        vectors.isEmpty() ? 0 : vectors.get(0).length);
                return vectors;
            }
            
            log.error("批量获取文本向量失败: texts_count={}, status={}, body={}", 
                     texts.size(), response.getStatus(), response.body());
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("批量获取文本向量异常: texts_count={}", texts.size(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查Embedding服务是否可用
     * @return 是否可用
     */
    public boolean isServiceAvailable() {
        try {
            String url = xinferenceBaseUrl + "/v1/models";
            HttpResponse response = HttpRequest.get(url)
                    .timeout(5000) // 5秒超时
                    .execute();

            if (response.getStatus() == 200) {
                JSONObject result = JSONUtil.parseObj(response.body());
                JSONArray data = result.getJSONArray("data");
                
                // 检查是否包含我们需要的模型
                for (int i = 0; i < data.size(); i++) {
                    JSONObject model = data.getJSONObject(i);
                    if (embeddingModel.equals(model.getStr("id"))) {
                        log.info("Embedding服务可用: model={}", embeddingModel);
                        return true;
                    }
                }
                
                log.warn("Embedding服务中未找到指定模型: model={}", embeddingModel);
                return false;
            }
            
            log.error("Embedding服务不可用: status={}", response.getStatus());
            return false;
        } catch (Exception e) {
            log.error("检查Embedding服务可用性异常", e);
            return false;
        }
    }
}
