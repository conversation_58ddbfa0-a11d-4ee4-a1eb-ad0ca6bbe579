package com.jd.qf.ai.knowledge.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.qf.ai.knowledge.common.dto.KnowledgeBaseDTO;
import com.jd.qf.ai.knowledge.common.dto.request.CreateKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.dto.request.QueryKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.dto.request.UpdateKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.entity.KnowledgeBase;
import com.jd.qf.ai.knowledge.dao.KnowledgeBaseMapper;
import com.jd.qf.ai.knowledge.retrive.VearchService;
import com.jd.qf.ai.knowledge.service.KnowledgeBaseService;
import com.jd.qf.ai.knowledge.service.KnowledgeSegmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 知识库服务实现
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
@Slf4j
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    private KnowledgeSegmentService knowledgeSegmentService;

    @Autowired
    private VearchService vearchService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeBaseDTO createKnowledgeBase(CreateKnowledgeBaseRequest request) {
        // 生成知识库ID
        String baseNo = generateBaseNo();

        // 创建知识库实体
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setBaseNo(baseNo);
        knowledgeBase.setAppId(request.getAppId());
        knowledgeBase.setName(request.getName());
        knowledgeBase.setDesc(request.getDesc());
        knowledgeBase.setCreator(request.getCreator());
        knowledgeBase.setModifier(request.getCreator());
        knowledgeBase.setCreatedTime(LocalDateTime.now());
        knowledgeBase.setModifiedTime(LocalDateTime.now());

        // 保存到数据库
        int result = knowledgeBaseMapper.insert(knowledgeBase);
        if (result <= 0) {
            throw new RuntimeException("创建知识库失败");
        }

        // 在Vearch中创建表空间
        boolean vearchResult = vearchService.createSpace(request.getAppId(), baseNo);
        if (!vearchResult) {
            log.warn("创建Vearch表空间失败，但知识库已创建: appId={}, baseNo={}", request.getAppId(), baseNo);
        }

        // 转换为DTO返回
        KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
        BeanUtils.copyProperties(knowledgeBase, dto);

        log.info("成功创建知识库: appId={}, baseNo={}, name={}", request.getAppId(), baseNo, request.getName());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateKnowledgeBase(UpdateKnowledgeBaseRequest request) {
        // 查询现有知识库
        KnowledgeBase existingBase = knowledgeBaseMapper.selectByAppIdAndBaseNo(request.getAppId(), request.getBaseNo());
        if (existingBase == null) {
            throw new RuntimeException("知识库不存在");
        }

        // 更新字段
        KnowledgeBase updateBase = new KnowledgeBase();
        updateBase.setId(existingBase.getId());
        if (StrUtil.isNotBlank(request.getName())) {
            updateBase.setName(request.getName());
        }
        if (StrUtil.isNotBlank(request.getDesc())) {
            updateBase.setDesc(request.getDesc());
        }
        updateBase.setModifier(request.getModifier());
        updateBase.setModifiedTime(LocalDateTime.now());

        int result = knowledgeBaseMapper.updateById(updateBase);
        if (result > 0) {
            log.info("成功更新知识库: appId={}, baseNo={}", request.getAppId(), request.getBaseNo());
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteKnowledgeBase(String appId, String baseNo, String modifier) {
        // 查询知识库是否存在
        KnowledgeBase existingBase = knowledgeBaseMapper.selectByAppIdAndBaseNo(appId, baseNo);
        if (existingBase == null) {
            throw new RuntimeException("知识库不存在");
        }

        // 删除知识库下的所有分段
        knowledgeSegmentService.deleteSegmentsByBaseNo(appId, baseNo, modifier);

        // 逻辑删除知识库
        KnowledgeBase updateBase = new KnowledgeBase();
        updateBase.setId(existingBase.getId());
        updateBase.setValid(0);
        updateBase.setModifier(modifier);
        updateBase.setModifiedTime(LocalDateTime.now());

        int result = knowledgeBaseMapper.updateById(updateBase);
        if (result > 0) {
            // 删除Vearch表空间
            boolean vearchResult = vearchService.deleteSpace(appId, baseNo);
            if (!vearchResult) {
                log.warn("删除Vearch表空间失败: appId={}, baseNo={}", appId, baseNo);
            }

            log.info("成功删除知识库: appId={}, baseNo={}", appId, baseNo);
            return true;
        }

        return false;
    }

    @Override
    public KnowledgeBaseDTO getKnowledgeBase(String appId, String baseNo) {
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectByAppIdAndBaseNo(appId, baseNo);
        if (knowledgeBase == null) {
            return null;
        }

        KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
        BeanUtils.copyProperties(knowledgeBase, dto);
        return dto;
    }

    @Override
    public IPage<KnowledgeBaseDTO> queryKnowledgeBases(QueryKnowledgeBaseRequest request) {
        Page<KnowledgeBase> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<KnowledgeBase> result = knowledgeBaseMapper.selectPageByAppId(page, request.getAppId(), request.getName());

        // 转换为DTO
        return result.convert(entity -> {
            KnowledgeBaseDTO dto = new KnowledgeBaseDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });
    }

    @Override
    public boolean existsKnowledgeBase(String appId, String baseNo) {
        return knowledgeBaseMapper.existsByAppIdAndBaseNo(appId, baseNo);
    }

    /**
     * 生成知识库ID
     */
    private String generateBaseNo() {
        return "KB_" + IdUtil.getSnowflakeNextIdStr();
    }
}
