package com.jd.qf.ai.knowledge.retrive;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.jd.qf.ai.knowledge.common.dto.response.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch服务
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
@Slf4j
public class ElasticsearchService {

    @Autowired
    private RestHighLevelClient elasticsearchClient;

    private static final String INDEX_NAME = "knowledge_segments";

    /**
     * 添加分段到ES
     */
    public void addSegment(String appId, String baseNo, String segmentNo, String content, String desc, String metadata) {
        try {
            Map<String, Object> document = new HashMap<>();
            document.put("appId", appId);
            document.put("baseNo", baseNo);
            document.put("segmentNo", segmentNo);
            document.put("content", content);
            document.put("desc", desc);
            document.put("metadata", metadata);
            document.put("timestamp", System.currentTimeMillis());

            IndexRequest request = new IndexRequest(INDEX_NAME)
                    .id(segmentNo)
                    .source(document, XContentType.JSON);

            elasticsearchClient.index(request, RequestOptions.DEFAULT);
            log.info("成功添加分段到ES: segmentNo={}", segmentNo);
        } catch (IOException e) {
            log.error("添加分段到ES失败: segmentNo={}", segmentNo, e);
            throw new RuntimeException("添加分段到ES失败", e);
        }
    }

    /**
     * 更新ES中的分段
     */
    public void updateSegment(String segmentNo, String content, String desc, String metadata) {
        try {
            Map<String, Object> document = new HashMap<>();
            if (StrUtil.isNotBlank(content)) {
                document.put("content", content);
            }
            if (StrUtil.isNotBlank(desc)) {
                document.put("desc", desc);
            }
            if (StrUtil.isNotBlank(metadata)) {
                document.put("metadata", metadata);
            }
            document.put("timestamp", System.currentTimeMillis());

            UpdateRequest request = new UpdateRequest(INDEX_NAME, segmentNo)
                    .doc(document, XContentType.JSON);

            elasticsearchClient.update(request, RequestOptions.DEFAULT);
            log.info("成功更新ES中的分段: segmentNo={}", segmentNo);
        } catch (IOException e) {
            log.error("更新ES中的分段失败: segmentNo={}", segmentNo, e);
            throw new RuntimeException("更新ES中的分段失败", e);
        }
    }

    /**
     * 从ES删除分段
     */
    public void deleteSegment(String segmentNo) {
        try {
            DeleteRequest request = new DeleteRequest(INDEX_NAME, segmentNo);
            elasticsearchClient.delete(request, RequestOptions.DEFAULT);
            log.info("成功从ES删除分段: segmentNo={}", segmentNo);
        } catch (IOException e) {
            log.error("从ES删除分段失败: segmentNo={}", segmentNo, e);
            throw new RuntimeException("从ES删除分段失败", e);
        }
    }

    /**
     * 全文检索
     */
    public List<SearchResult> fullTextSearch(String appId, String baseNo, String query, int topK) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("appId", appId))
                    .must(QueryBuilders.termQuery("baseNo", baseNo))
                    .must(QueryBuilders.multiMatchQuery(query, "content", "desc"));

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQuery)
                    .size(topK);

            SearchRequest searchRequest = new SearchRequest(INDEX_NAME)
                    .source(sourceBuilder);

            SearchResponse response = elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);

            List<SearchResult> results = new ArrayList<>();
            for (SearchHit hit : response.getHits().getHits()) {
                SearchResult result = new SearchResult();
                Map<String, Object> source = hit.getSourceAsMap();
                result.setSegmentNo((String) source.get("segmentNo"));
                result.setContent((String) source.get("content"));
                result.setDesc((String) source.get("desc"));
                result.setMetadata((String) source.get("metadata"));
                result.setScore((double) hit.getScore());
                result.setSearchType("FULL_TEXT");
                results.add(result);
            }

            log.info("全文检索完成: appId={}, baseNo={}, query={}, 结果数量={}", appId, baseNo, query, results.size());
            return results;
        } catch (IOException e) {
            log.error("全文检索失败: appId={}, baseNo={}, query={}", appId, baseNo, query, e);
            throw new RuntimeException("全文检索失败", e);
        }
    }

    /**
     * 根据知识库ID删除所有分段
     */
    public void deleteSegmentsByBaseNo(String appId, String baseNo) {
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("appId", appId))
                    .must(QueryBuilders.termQuery("baseNo", baseNo));

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQuery)
                    .size(10000); // 假设单个知识库不会超过10000个分段

            SearchRequest searchRequest = new SearchRequest(INDEX_NAME)
                    .source(sourceBuilder);

            SearchResponse response = elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);

            for (SearchHit hit : response.getHits().getHits()) {
                deleteSegment(hit.getId());
            }

            log.info("成功删除知识库下所有分段: appId={}, baseNo={}", appId, baseNo);
        } catch (IOException e) {
            log.error("删除知识库下所有分段失败: appId={}, baseNo={}", appId, baseNo, e);
            throw new RuntimeException("删除知识库下所有分段失败", e);
        }
    }
}
