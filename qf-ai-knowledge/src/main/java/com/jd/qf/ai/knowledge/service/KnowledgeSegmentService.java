package com.jd.qf.ai.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.qf.ai.knowledge.common.dto.KnowledgeSegmentDTO;
import com.jd.qf.ai.knowledge.common.dto.request.CreateSegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.request.QuerySegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.request.SearchKnowledgeRequest;
import com.jd.qf.ai.knowledge.common.dto.request.UpdateSegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.response.SearchResult;

import java.util.List;

/**
 * 知识分段服务接口
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface KnowledgeSegmentService {

    /**
     * 创建分段
     * @param request 创建请求
     * @return 分段信息
     */
    KnowledgeSegmentDTO createSegment(CreateSegmentRequest request);

    /**
     * 更新分段
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updateSegment(UpdateSegmentRequest request);

    /**
     * 删除分段
     * @param appId 应用ID
     * @param segmentNo 分段ID
     * @param modifier 操作人
     * @return 是否成功
     */
    boolean deleteSegment(String appId, String segmentNo, String modifier);

    /**
     * 根据ID查询分段
     * @param appId 应用ID
     * @param segmentNo 分段ID
     * @return 分段信息
     */
    KnowledgeSegmentDTO getSegment(String appId, String segmentNo);

    /**
     * 分页查询分段
     * @param request 查询请求
     * @return 分页结果
     */
    IPage<KnowledgeSegmentDTO> querySegments(QuerySegmentRequest request);

    /**
     * 根据知识库ID删除所有分段
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @param modifier 操作人
     * @return 是否成功
     */
    boolean deleteSegmentsByBaseNo(String appId, String baseNo, String modifier);

    /**
     * 检索知识库
     * @param request 检索请求
     * @return 检索结果
     */
    List<SearchResult> searchKnowledge(SearchKnowledgeRequest request);

    /**
     * 检查分段是否存在
     * @param appId 应用ID
     * @param segmentNo 分段ID
     * @return 是否存在
     */
    boolean existsSegment(String appId, String segmentNo);
}
