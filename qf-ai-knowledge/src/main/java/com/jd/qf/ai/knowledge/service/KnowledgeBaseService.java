package com.jd.qf.ai.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jd.qf.ai.knowledge.common.dto.KnowledgeBaseDTO;
import com.jd.qf.ai.knowledge.common.dto.request.CreateKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.dto.request.QueryKnowledgeBaseRequest;
import com.jd.qf.ai.knowledge.common.dto.request.UpdateKnowledgeBaseRequest;

/**
 * 知识库服务接口
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface KnowledgeBaseService {

    /**
     * 创建知识库
     * @param request 创建请求
     * @return 知识库信息
     */
    KnowledgeBaseDTO createKnowledgeBase(CreateKnowledgeBaseRequest request);

    /**
     * 更新知识库
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updateKnowledgeBase(UpdateKnowledgeBaseRequest request);

    /**
     * 删除知识库
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @param modifier 操作人
     * @return 是否成功
     */
    boolean deleteKnowledgeBase(String appId, String baseNo, String modifier);

    /**
     * 根据ID查询知识库
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @return 知识库信息
     */
    KnowledgeBaseDTO getKnowledgeBase(String appId, String baseNo);

    /**
     * 分页查询知识库
     * @param request 查询请求
     * @return 分页结果
     */
    IPage<KnowledgeBaseDTO> queryKnowledgeBases(QueryKnowledgeBaseRequest request);

    /**
     * 检查知识库是否存在
     * @param appId 应用ID
     * @param baseNo 知识库ID
     * @return 是否存在
     */
    boolean existsKnowledgeBase(String appId, String baseNo);
}
