package com.jd.qf.ai.knowledge.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识库实体类
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("knowledge_base")
public class KnowledgeBase {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 库ID
     */
    @TableField("base_no")
    private String baseNo;

    /**
     * app ID
     */
    @TableField("appId")
    private String appId;

    /**
     * 库名称
     */
    @TableField("name")
    private String name;

    /**
     * 库描述
     */
    @TableField("desc")
    private String desc;

    /**
     * 逻辑删除标志,1:正常记录,0:已逻辑删除
     */
    @TableField("valid")
    @TableLogic(value = "1", delval = "0")
    private Integer valid;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 最后更新人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedTime;
}
