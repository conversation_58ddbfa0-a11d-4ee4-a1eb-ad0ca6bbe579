package com.jd.qf.ai.knowledge.common.annotation;

import java.lang.annotation.*;

/**
 * 鉴权注解
 * <AUTHOR>
 * @date 2025-01-02
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidateAuth {
    
    /**
     * 是否必须鉴权，默认true
     */
    boolean required() default true;
    
    /**
     * 鉴权描述
     */
    String value() default "";
}
