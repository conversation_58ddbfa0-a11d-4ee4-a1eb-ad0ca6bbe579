package com.jd.qf.ai.knowledge.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 知识分段DTO
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class KnowledgeSegmentDTO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * app ID
     */
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * 库ID
     */
    @NotBlank(message = "知识库ID不能为空")
    private String baseNo;

    /**
     * 分段ID
     */
    private String segmentNo;

    /**
     * 分段内容
     */
    @NotBlank(message = "分段内容不能为空")
    private String content;

    /**
     * 分段描述
     */
    @Size(max = 512, message = "分段描述长度不能超过512个字符")
    private String desc;

    /**
     * 元数据(JSON格式)
     */
    private String metadata;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 最后更新人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifiedTime;
}
