package com.jd.qf.ai.knowledge.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.qf.ai.knowledge.common.dto.KnowledgeSegmentDTO;
import com.jd.qf.ai.knowledge.common.dto.request.CreateSegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.request.QuerySegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.request.SearchKnowledgeRequest;
import com.jd.qf.ai.knowledge.common.dto.request.UpdateSegmentRequest;
import com.jd.qf.ai.knowledge.common.dto.response.SearchResult;
import com.jd.qf.ai.knowledge.common.entity.KnowledgeSegment;
import com.jd.qf.ai.knowledge.common.enums.SearchTypeEnum;
import com.jd.qf.ai.knowledge.dao.KnowledgeSegmentMapper;
import com.jd.qf.ai.knowledge.embedding.EmbeddingService;
import com.jd.qf.ai.knowledge.rerank.RerankService;
import com.jd.qf.ai.knowledge.retrive.ElasticsearchService;
import com.jd.qf.ai.knowledge.retrive.VearchService;
import com.jd.qf.ai.knowledge.service.KnowledgeBaseService;
import com.jd.qf.ai.knowledge.service.KnowledgeSegmentService;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识分段服务实现
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
@Slf4j
public class KnowledgeSegmentServiceImpl implements KnowledgeSegmentService {

    @Autowired
    private KnowledgeSegmentMapper knowledgeSegmentMapper;

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Autowired
    private VearchService vearchService;

    @Autowired
    private EmbeddingService embeddingService;

    @Autowired
    private RerankService rerankService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public KnowledgeSegmentDTO createSegment(CreateSegmentRequest request) {
        // 验证知识库是否存在
        if (!knowledgeBaseService.existsKnowledgeBase(request.getAppId(), request.getBaseNo())) {
            throw new BizException("知识库不存在");
        }

        // 生成分段ID
        String segmentNo = generateSegmentNo();

        // 创建分段实体
        KnowledgeSegment segment = new KnowledgeSegment();
        String metadateJson = JSON.toJSONString(request.getMetadata());
        segment.setAppId(request.getAppId());
        segment.setBaseNo(request.getBaseNo());
        segment.setSegmentNo(segmentNo);
        segment.setContent(request.getContent());
        segment.setDesc(request.getDesc());
        segment.setMetadata(metadateJson);
        segment.setCreator(request.getCreator());
        segment.setModifier(request.getCreator());
        segment.setCreatedTime(LocalDateTime.now());
        segment.setModifiedTime(LocalDateTime.now());

        // 保存到数据库
        int result = knowledgeSegmentMapper.insert(segment);
        if (result <= 0) {
            throw new BizException("创建分段失败");
        }

        // 异步处理向量化和索引
        processSegmentIndexing(request.getAppId(), request.getBaseNo(), segmentNo, 
                              request.getContent(), request.getDesc(), metadateJson);

        // 转换为DTO返回
        KnowledgeSegmentDTO dto = new KnowledgeSegmentDTO();
        BeanUtils.copyProperties(segment, dto);

        log.info("成功创建分段: appId={}, baseNo={}, segmentNo={}", request.getAppId(), request.getBaseNo(), segmentNo);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSegment(UpdateSegmentRequest request) {
        // 查询现有分段
        KnowledgeSegment existingSegment = knowledgeSegmentMapper.selectByAppIdAndSegmentNo(request.getAppId(), request.getSegmentNo());
        if (existingSegment == null) {
            throw new BizException("分段不存在");
        }

        // 更新字段
        KnowledgeSegment updateSegment = new KnowledgeSegment();
        updateSegment.setId(existingSegment.getId());
        boolean needReindex = false;

        if (StrUtil.isNotBlank(request.getContent())) {
            updateSegment.setContent(request.getContent());
            needReindex = true;
        }
        if (StrUtil.isNotBlank(request.getDesc())) {
            updateSegment.setDesc(request.getDesc());
        }
        if (StrUtil.isNotBlank(request.getMetadata())) {
            updateSegment.setMetadata(request.getMetadata());
        }
        updateSegment.setModifier(request.getModifier());
        updateSegment.setModifiedTime(LocalDateTime.now());

        int result = knowledgeSegmentMapper.updateById(updateSegment);
        if (result > 0) {
            // 如果内容发生变化，需要重新索引
            if (needReindex) {
                String content = StrUtil.isNotBlank(request.getContent()) ? request.getContent() : existingSegment.getContent();
                String desc = StrUtil.isNotBlank(request.getDesc()) ? request.getDesc() : existingSegment.getDesc();
                String metadata = StrUtil.isNotBlank(request.getMetadata()) ? request.getMetadata() : existingSegment.getMetadata();
                
                processSegmentIndexing(request.getAppId(), existingSegment.getBaseNo(), request.getSegmentNo(),
                                     content, desc, metadata);
            }

            log.info("成功更新分段: appId={}, segmentNo={}", request.getAppId(), request.getSegmentNo());
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSegment(String appId, String segmentNo, String modifier) {
        // 查询分段是否存在
        KnowledgeSegment existingSegment = knowledgeSegmentMapper.selectByAppIdAndSegmentNo(appId, segmentNo);
        if (existingSegment == null) {
            throw new BizException("分段不存在");
        }

        // 逻辑删除分段
        KnowledgeSegment updateSegment = new KnowledgeSegment();
        updateSegment.setId(existingSegment.getId());
        updateSegment.setValid(0);
        updateSegment.setModifier(modifier);
        updateSegment.setModifiedTime(LocalDateTime.now());

        int result = knowledgeSegmentMapper.updateById(updateSegment);
        if (result > 0) {
            // 从ES和Vearch中删除
            try {
                elasticsearchService.deleteSegment(segmentNo);
                vearchService.deleteDocument(appId, existingSegment.getBaseNo(), segmentNo);
            } catch (Exception e) {
                log.error("删除分段索引失败: appId={}, segmentNo={}", appId, segmentNo, e);
            }

            log.info("成功删除分段: appId={}, segmentNo={}", appId, segmentNo);
            return true;
        }

        return false;
    }

    @Override
    public KnowledgeSegmentDTO getSegment(String appId, String segmentNo) {
        KnowledgeSegment segment = knowledgeSegmentMapper.selectByAppIdAndSegmentNo(appId, segmentNo);
        if (segment == null) {
            return null;
        }

        KnowledgeSegmentDTO dto = new KnowledgeSegmentDTO();
        BeanUtils.copyProperties(segment, dto);
        return dto;
    }

    @Override
    public IPage<KnowledgeSegmentDTO> querySegments(QuerySegmentRequest request) {
        Page<KnowledgeSegment> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<KnowledgeSegment> result = knowledgeSegmentMapper.selectPageByBaseNo(page, 
                request.getAppId(), request.getBaseNo(), request.getKeyword());

        // 转换为DTO
        IPage<KnowledgeSegmentDTO> dtoPage = result.convert(entity -> {
            KnowledgeSegmentDTO dto = new KnowledgeSegmentDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });

        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSegmentsByBaseNo(String appId, String baseNo, String modifier) {
        // 查询所有分段
        List<KnowledgeSegment> segments = knowledgeSegmentMapper.selectByBaseNo(appId, baseNo);
        
        // 逻辑删除数据库中的分段
        int result = knowledgeSegmentMapper.deleteByBaseNo(appId, baseNo);
        
        if (result > 0) {
            // 从ES和Vearch中删除
            try {
                elasticsearchService.deleteSegmentsByBaseNo(appId, baseNo);
                // Vearch的表空间会在删除知识库时一起删除
            } catch (Exception e) {
                log.error("删除知识库分段索引失败: appId={}, baseNo={}", appId, baseNo, e);
            }

            log.info("成功删除知识库下所有分段: appId={}, baseNo={}, count={}", appId, baseNo, segments.size());
            return true;
        }

        return false;
    }

    @Override
    public List<SearchResult> searchKnowledge(SearchKnowledgeRequest request) {
        SearchTypeEnum searchType = SearchTypeEnum.getByCode(request.getSearchType());
        if (searchType == null) {
            throw new BizException("不支持的检索类型: " + request.getSearchType());
        }

        List<SearchResult> results = switch (searchType) {
            case FULL_TEXT -> elasticsearchService.fullTextSearch(request.getAppId(), request.getBaseNo(),
                    request.getQuery(), request.getTopK());
            case VECTOR -> performVectorSearch(request);
            case HYBRID -> performHybridSearch(request);
        };

        log.info("知识库检索完成: appId={}, baseNo={}, searchType={}, query={}, 结果数量={}",
                request.getAppId(), request.getBaseNo(), request.getSearchType(), 
                request.getQuery().substring(0, Math.min(50, request.getQuery().length())), results.size());

        return results;
    }

    @Override
    public boolean existsSegment(String appId, String segmentNo) {
        return knowledgeSegmentMapper.existsByAppIdAndSegmentNo(appId, segmentNo);
    }

    /**
     * 处理分段索引（向量化和存储到ES、Vearch）
     */
    private void processSegmentIndexing(String appId, String baseNo, String segmentNo, 
                                      String content, String desc, String metadata) {
        try {
            // 添加到ES
            elasticsearchService.addSegment(appId, baseNo, segmentNo, content, desc, metadata);

            // 生成向量并添加到Vearch
            double[] vector = embeddingService.getEmbedding(content);
            if (vector != null) {
                vearchService.addDocument(appId, baseNo, segmentNo, content, vector, metadata);
            } else {
                log.warn("生成向量失败，跳过Vearch索引: segmentNo={}", segmentNo);
            }
        } catch (Exception e) {
            log.error("处理分段索引失败: segmentNo={}", segmentNo, e);
        }
    }

    /**
     * 执行向量检索
     */
    private List<SearchResult> performVectorSearch(SearchKnowledgeRequest request) {
        double[] queryVector = embeddingService.getEmbedding(request.getQuery());
        if (queryVector == null) {
            log.error("生成查询向量失败: query={}", request.getQuery());
            return new ArrayList<>();
        }

        return vearchService.vectorSearch(request.getAppId(), request.getBaseNo(), queryVector, request.getTopK());
    }

    /**
     * 执行混合检索
     */
    private List<SearchResult> performHybridSearch(SearchKnowledgeRequest request) {
        // 同时进行全文检索和向量检索
        List<SearchResult> fullTextResults = elasticsearchService.fullTextSearch(
                request.getAppId(), request.getBaseNo(), request.getQuery(), request.getTopK());
        
        List<SearchResult> vectorResults = performVectorSearch(request);

        // 合并结果并去重
        List<SearchResult> combinedResults = new ArrayList<>(fullTextResults);
        
        for (SearchResult vectorResult : vectorResults) {
            boolean exists = combinedResults.stream()
                    .anyMatch(r -> r.getSegmentNo().equals(vectorResult.getSegmentNo()));
            if (!exists) {
                combinedResults.add(vectorResult);
            }
        }

        // 使用rerank模型重排序
        return rerankService.rerank(request.getQuery(), combinedResults, request.getTopN());
    }

    /**
     * 生成分段ID
     */
    private String generateSegmentNo() {
        return "SEG_" + IdUtil.getSnowflakeNextIdStr();
    }
}
